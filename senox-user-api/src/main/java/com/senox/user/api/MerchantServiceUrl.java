package com.senox.user.api;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
public class MerchantServiceUrl {
    private MerchantServiceUrl() {
    }

    public static final String MERCHANT_ADD = "/merchant/add";
    public static final String MERCHANT_BATCH_ADD = "/merchant/batchAdd";
    public static final String MERCHANT_UPDATE = "/merchant/update";
    public static final String MERCHANT_DELETE = "/merchant/delete";
    public static final String MERCHANT_SAVE = "/merchant/save";
    public static final String MERCHANT_GET ="/merchant/get/{id}";
    public static final String MERCHANT_GET_BY_RC ="/merchant/getByRc";
    public static final String MERCHANT_GET_BY_RC_LIST ="/merchant/getByRcList";
    public static final String MERCHANT_GET_BY_CONTACT ="/merchant/getByContact";
    public static final String MERCHANT_GET_BY_NAME ="/merchant/getByName";
    public static final String MERCHANT_LIST = "/merchant/list";
    public static final String MERCHANT_VIEW_LIST = "/merchant/listView";
    public static final String MERCHANT_PAGE = "/merchant/page";

    public static final String MERCHANT_AUTH_APPLY_ADD = "/merchant/auth/apply/add";
    public static final String MERCHANT_AUTH_APPLY_UPDATE = "/merchant/auth/apply/update";
    public static final String MERCHANT_AUTH_APPLY_DELETE = "/merchant/auth/apply/delete/{id}";
    public static final String MERCHANT_AUTH_APPLY_AUDIT = "/merchant/auth/apply/audit";
    public static final String MERCHANT_AUTH_APPLY_GET = "/merchant/auth/apply/get/{id}";
    public static final String MERCHANT_APPLY_COUNT = "/merchant/auth/apply/count";
    public static final String MERCHANT_APPLY_LIST = "/merchant/auth/apply/list";
    public static final String MERCHANT_APPLY_PAGE = "/merchant/auth/apply/page";
    public static final String MERCHANT_APPLY_AUDIT_COUNT = "/merchant/auth/apply/audit/count";
    public static final String MERCHANT_UPDATE_CHARGES = "/merchant/update/charges";

}
