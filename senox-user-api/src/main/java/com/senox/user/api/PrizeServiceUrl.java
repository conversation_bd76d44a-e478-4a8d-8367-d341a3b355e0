package com.senox.user.api;

/**
 * <AUTHOR>
 * @date 2025/4/15 14:37
 */
public class PrizeServiceUrl {
    private PrizeServiceUrl(){

    }

    public static final String PRIZE_ADD = "/prize/add";
    public static final String PRIZE_UPDATE = "/prize/update";
    public static final String PRIZE_GET = "/prize/get/{id}";
    public static final String PRIZE_DELETE = "/prize/delete/{id}";
    public static final String PRIZE_PAGE = "/prize/page";
    public static final String PRIZE_DRAW = "/prize/draw/{activityId}";
    public static final String PRIZE_INCREASE_DRAW_NUMBER = "/prize/increase/drawNumber";
    public static final String PRIZE_DRAW_NUMBER_COUNT = "/prize/drawNumber/count";
    public static final String PRIZE_AVAILABLE_NUMBER = "/prize/availableNumber/{activityId}";
    public static final String PRIZE_RECORDS_GET = "/prize/records/get/{id}";
    public static final String PRIZE_RECORDS_BY_UUID = "/prize/records/byUuid";
    public static final String PRIZE_RECORDS_PAGE = "/prize/records/page";
    public static final String PRIZE_VERIFY_PRIZE = "/prize/verify/prize";
}
