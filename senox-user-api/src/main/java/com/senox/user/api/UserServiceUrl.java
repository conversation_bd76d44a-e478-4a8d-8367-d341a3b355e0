package com.senox.user.api;

/**
 * <AUTHOR>
 * @Date 2021/1/6 12:00
 */
public class UserServiceUrl {

    private UserServiceUrl() {
    }

    /**
     * 服务名
     */
    public static final String SERVICE_NAME = "senox-user";

    // ----------- 客户管理 接口 --------------
    public static final String CUSTOMER_ADD = "/customer/add";
    public static final String CUSTOMER_UPDATE = "/customer/update";
    public static final String CUSTOMER_GET_BY_ID = "/customer/get/{id}";
    public static final String CUSTOMER_GET_BY_IDCARD = "/customer/getByIdcard";
    public static final String CUSTOMER_GET_BY_SERIAL = "/customer/getBySerial";
    public static final String CUSTOMER_LIST = "/customer/list";
    public static final String CUSTOMER_LIST_NO_PAGE = "/customer/listNoPage";

    public static final String IC_CARD_LIST = "/iccard/list";

    public static final String WXUSER_GRAY_SET = "/wxUser/gray/set/{userId}";
    public static final String WXUSER_GRAY_CANCEL = "/wxUser/gray/cancel/{userId}";
    public static final String WXUSER_FIND_BY_OPENID = "/wxUser/getByOpenid";
    public static final String WXUSER_LIST = "/wxUser/list";
    public static final String WXUSER_REALTY_BIND = "/wxUser/realty/bind";
    public static final String WXUSER_REALTY_UNBIND = "/wxUser/realty/unbind";
    public static final String WXUSER_REALTY_LIST = "/wxUser/listRealty/{userId}";
    public static final String WXUSER_UPDATE_REMARK = "/wxUser/update/remark";
    public static final String WXUSER_UNBIND_RIDER = "/wxUser/unbindRider/{riderId}";

    // ----------- 公司内部管理 --------------
    public static final String COMPANY_ADD = "/company/add";
    public static final String COMPANY_UPDATE = "/company/update";
    public static final String COMPANY_GET = "/company/get/{id}";
    public static final String COMPANY_LIST = "/company/list";

    public static final String DEPARTMENT_ADD = "/department/add";
    public static final String DEPARTMENT_UPDATE = "/department/update";
    public static final String DEPARTMENT_GET = "/department/get/{id}";
    public static final String DEPARTMENT_LIST = "/department/list/{parentId}";
    public static final String DEPARTMENT_LIST_ALL = "/department/listAll";
    public static final String DEPARTMENT_TREE = "/department/treeList";

    public static final String EMPLOYEE_ADD = "/employee/add";
    public static final String EMPLOYEE_UPDATE = "/employee/update";
    public static final String EMPLOYEE_GET = "/employee/get/{id}";
    public static final String EMPLOYEE_LIST = "/employee/list";

    public static final String BOOKING_MEAL_LIST = "/bookingMeal/list";
    public static final String BOOKING_MEAL_DAY_REPORT = "/bookingMeal/dayReport";
    public static final String BOOKING_MEAL_DAY_REPORT_DETAIL = "/bookingMeal/dayReportDetail";
    public static final String DINING_INFORMATION_ADD_BATCH = "/diningInformation/addBatch";
    public static final String DINING_INFORMATION_ADD = "/diningInformation/add";
    public static final String DINING_INFORMATION_UPDATE = "/diningInformation/update";
    public static final String DINING_INFORMATION_DELETE = "/diningInformation/delete/{id}";
    public static final String DINING_INFORMATION_GET = "/diningInformation/get/{id}";
    public static final String DINING_INFORMATION_LIST = "/diningInformation/list";


    public static final String HOLIDAY_LIST = "/holiday/list";
    public static final String HOLIDAY_SAVE = "/holiday/save";
    public static final String HOLIDAY_DELETE = "/holiday/delete";

    // ----------- 管理用户 接口 --------------
    public static final String ADMIN_USER_CHECK_PASSWORD = "/adminUser/password/check";
    public static final String ADMIN_USER_MODIFY_PASSWORD = "/adminUser/password/modify";
    public static final String ADMIN_USER_CHECK_TOKEN = "/adminUser/token/check";
    public static final String ADMIN_USER_CHECK_FEIGN_TOKEN = "/adminUser/feignToken/check";
    public static final String ADMIN_USER_TOKEN_INVALID = "/adminUser/token/invalid";
    public static final String ADMIN_USER_FEIGN_TOKEN_INVALID = "/adminUser/feignToken/invalid";
    public static final String ADMIN_USER_CHECK_WECHAT_TOKEN = "/adminUser/wechatToken/check";
    public static final String ADMIN_USER_TOLL_MODIFY = "/adminUser/tollInfo/modify";
    public static final String ADMIN_USER_TOLL_GET = "/adminUser/tollInfo/get";
    public static final String ADMIN_USER_ADD = "/adminUser/add";
    public static final String ADMIN_USER_UPDATE = "/adminUser/update";
    public static final String ADMIN_USER_GET = "/adminUser/get/{id}";
    public static final String ADMIN_USER_GET_BY_REAL_NAME = "/adminUser/getByRealName";
    public static final String ADMIN_USER_GET_BY_TELEPHONE = "/adminUser/getByTelephone";
    public static final String ADMIN_USER_LIST = "/adminUser/list";
    public static final String ADMIN_USER_LIST_DEPARTMENT = "/adminUser/department/list/{userId}";
    public static final String ADMIN_USER_LIST_ROLE = "/adminUser/role/list/{userId}";
    public static final String ADMIN_USER_LIST_COS = "/adminUser/cos/list/{userId}";
    public static final String ROLE_LIST_USER = "/adminUser/user/list/{roleId}";

    public static final String ROLE_ADD = "/role/add";
    public static final String ROLE_UPDATE = "/role/update";
    public static final String ROLE_DELETE = "/role/delete/{id}";
    public static final String ROLE_GET = "/role/get/{id}";
    public static final String ROLE_LIST = "/role/list";

    public static final String COS_ADD = "/cos/add";
    public static final String COS_UPDATE = "/cos/update";
    public static final String COS_DELETE = "/cos/delete/{id}";
    public static final String COS_GET = "/cos/get/{id}";
    public static final String COS_LIST = "/cos/list";

    // ---------- 字典 接口 --------------
    public static final String AREA_ADD = "/area/add";
    public static final String AREA_UPDATE = "/area/update";
    public static final String AREA_GET = "/area/get/{id}";
    public static final String AREA_PROVINCE_LIST = "/area/province/list";
    public static final String AREA_CITY_LIST = "/area/city/list/{parentId}";

    public static final String PROFESSION_ADD = "/profession/add";
    public static final String PROFESSION_UPDATE = "/profession/update";
    public static final String PROFESSION_GET = "/profession/get/{id}";
    public static final String PROFESSION_LIST = "/profession/list";


    public static final String RESIDENT_ADD = "/resident/add";
    public static final String RESIDENT_UPDATE = "/resident/update";
    public static final String RESIDENT_UPDATE_FACE = "/resident/update/face";
    public static final String RESIDENT_DELETE = "/resident/delete/{id}";
    public static final String RESIDENT_GET = "/resident/get/{id}";
    public static final String RESIDENT_GETBYNO = "/resident/getByNo";
    public static final String RESIDENT_GETBYIDNUM = "/resident/getByIdNum";
    public static final String RESIDENT_LIST = "/resident/list";
    public static final String RESIDENT_ACCESS_ADD = "/resident/access/add";
    public static final String RESIDENT_ACCESS_DELETE = "/resident/access/deleteById";
    public static final String RESIDENT_ACCESS_BYNO = "/resident/access/byNo";
    public static final String RESIDENT_CHECK = "/resident/check";
    public static final String RESIDENT_ACCESS_DELETE_BY_CONTRACT_NO = "/resident/access/deleteByContractNo";
    public static final String RESIDENT_RENEWAL_ACCESS = "/resident/renewal/access";
    public static final String RESIDENT_ACCESS_SYNC = "/resident/access/sync/{deviceId}";

    public static final String ADMIN_REMOTE_ACCESS_ADD = "/remote/access/add";
    public static final String ADMIN_REMOTE_ACCESS_DELETE = "/remote/access/delete/{id}";
    public static final String ADMIN_REMOTE_ACCESS_GET = "/remote/access/get/{id}";
    public static final String ADMIN_REMOTE_ACCESS_LIST = "/remote/access/list";

    // -------------- 经营户 ------------------
    public static final String ENTERPRISE_SAVE = "/enterprise/save";
    public static final String ENTERPRISE_DELETE = "/enterprise/delete/{id}";
    public static final String ENTERPRISE_FIREFIGHTING_EMPHASIS_SAVE = "/enterprise/firefighting/emphasis/save";
    public static final String ENTERPRISE_FIREFIGHTING_EMPHASIS_CANCEL = "/enterprise/firefighting/emphasis/cancel";
    public static final String ENTERPRISE_GET = "/enterprise/get/{id}";
    public static final String ENTERPRISE_LIST = "/enterprise/list";
    public static final String ENTERPRISE_PAGE = "/enterprise/page";
    public static final String ENTERPRISE_REALTY_LIST = "/enterprise/realty/list";

    public static final String BUSINESS_CATEGORY_ADD = "/dict/businessCategory/add";
    public static final String BUSINESS_CATEGORY_UPDATE = "/dict/businessCategory/update";
    public static final String BUSINESS_CATEGORY_DELETE = "/dict/businessCategory/delete/{id}";
    public static final String BUSINESS_CATEGORY_LIST = "/dict/businessCategory/list";

    // --------------- 建议反馈------------------
    public static final String FEED_BACK_ADD = "/feed/back/add";
    public static final String FEED_BACK_GET = "/feed/back/get/{id}/{isDetail}";
    public static final String FEED_BACK_LIST = "/feed/back/list";
    public static final String FEED_BACK_REPLY_ADD = "/feed/back/reply/add";
    public static final String FEED_BACK_REPLY_GET = "/feed/back/reply/get/{id}";


    public static final String FEIGN_INVOKE_GET = "/feignInvoke/get";
}
