package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.ActivityServiceUrl;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.ActivitySearchVo;
import com.senox.user.vo.ActivityVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/4/10 10:20
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface ActivityClient {

    /**
     * 添加活动
     * @param activityVo
     * @return
     */
    @PostMapping(ActivityServiceUrl.ACTIVITY_ADD)
    Long addActivity(@RequestBody ActivityVo activityVo);

    /**
     * 更新活动
     * @param activityVo
     */
    @PostMapping(ActivityServiceUrl.ACTIVITY_UPDATE)
    void updateActivity(@RequestBody ActivityVo activityVo);

    /**
     * 生成活动链接
     * @param id
     * @return
     */
    @GetMapping(ActivityServiceUrl.ACTIVITY_GENERATE)
    String generateActivityUrl(@PathVariable Long id);

    /**
     * 根据id获取活动
     * @param id
     * @return
     */
    @GetMapping(ActivityServiceUrl.ACTIVITY_GET)
    ActivityVo findActivityById(@PathVariable Long id);

    /**
     * 根据uuid获取活动
     * @param uuid
     * @return
     */
    @GetMapping(ActivityServiceUrl.ACTIVITY_GET_BY_UUID)
    ActivityVo findActivityByUuid(@PathVariable String uuid);

    /**
     * 根据id删除活动
     * @param id
     */
    @GetMapping(ActivityServiceUrl.ACTIVITY_DELETE)
    void deleteActivityById(@PathVariable Long id);

    /**
     * 活动分页
     * @param searchVo
     * @return
     */
    @PostMapping(ActivityServiceUrl.ACTIVITY_PAGE)
    PageResult<ActivityVo> pageActivityResult(@RequestBody ActivitySearchVo searchVo);
}
