package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理用户客户端
 * <AUTHOR>
 * @Date 2021/1/6 11:58
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface AdminUserClient {

    /**
     * 校验密码
     * @param loginVo
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_CHECK_PASSWORD)
    AdminUserVo checkPassword(@RequestBody AdminUserLoginVo loginVo);

    /**
     * 修改密码
     * @param pwdVo
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_MODIFY_PASSWORD)
    void modifyPassword(@RequestBody AdminUserChangePwdVo pwdVo);

    /**
     * 校验签名
     * @param token
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_CHECK_TOKEN)
    AdminUserDto checkToken(@RequestParam String token);

    /**
     * 失效签名
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_TOKEN_INVALID)
    void invalidToken(@RequestParam String token);

    /**
     * 校验feign签名
     * @param feignToken
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_CHECK_FEIGN_TOKEN)
    AdminUserDto checkFeignToken(@RequestParam String feignToken);

    /**
     * 失效feign签名
     * @param feignToken
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_FEIGN_TOKEN_INVALID)
    void invalidFeignToken(@RequestParam String feignToken);

    /**
     * 校验微信签名
     * @param appId
     * @param wechatToken
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_CHECK_WECHAT_TOKEN)
    AdminUserDto checkWechatToken(@RequestParam(value = "appId", required = false) String appId, @RequestParam("wechatToken") String wechatToken);

    /**
     * 校验微信签名
     * @param appId
     * @param openid
     * @return
     */
    @GetMapping(UserServiceUrl.WXUSER_FIND_BY_OPENID)
    WxUserVo checkWechatTk(@RequestParam(value = "appId", required = false) String appId, @RequestParam("openid") String openid);

    /**
     * 修改收费员支付信息
     * @param toll
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_TOLL_MODIFY)
    void modifyAdminToll(@RequestBody TollManSerialVo toll);

    /**
     * 获取收费员支付信息
     * @return
     */
    @GetMapping(UserServiceUrl.ADMIN_USER_TOLL_GET)
    TollManSerialVo findAdminToll();

    /**
     * 添加用户
     * @param userVo
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_ADD)
    Long addAdminUser(@RequestBody AdminUserVo userVo);

    /**
     * 更新用户
     * @param userVo
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_UPDATE)
    Long updateAdminUser(@RequestBody AdminUserVo userVo);

    /**
     * 获取用户信息
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.ADMIN_USER_GET)
    AdminUserVo getAdminUser(@PathVariable Long id);

    /**
     * 根据姓名获取用户信息
     * @param realName
     * @return
     */
    @GetMapping(UserServiceUrl.ADMIN_USER_GET_BY_REAL_NAME)
    AdminUserVo getByRealName(@RequestParam String realName);

    /**
     * 根据联系方式获取用户信息
     * @param telephone
     * @return
     */
    @GetMapping(UserServiceUrl.ADMIN_USER_GET_BY_TELEPHONE)
    AdminUserVo getAdminUserByTelephone(@RequestParam String telephone);

    /**
     * 用户列表
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_LIST)
    PageResult<AdminUserVo> listAdminUserPage(@RequestBody AdminUserSearchVo searchVo);

    /**
     * 用户部门列表
     * @param userId
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_LIST_DEPARTMENT)
    List<Long> listUserDepartment(@PathVariable Long userId);

    /**
     * 用户角色
     * @param userId
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_LIST_ROLE)
    List<Long> listUserRole(@PathVariable Long userId);

    /**
     * 用户权限
     * @param userId
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_USER_LIST_COS)
    List<String> listUserCos(@PathVariable Long userId);

    /**
     * 权限用户
     * @param roleId
     * @return
     */
    @GetMapping(UserServiceUrl.ROLE_LIST_USER)
    List<AdminUserVo> listRoleUser(@PathVariable Long roleId);
}
