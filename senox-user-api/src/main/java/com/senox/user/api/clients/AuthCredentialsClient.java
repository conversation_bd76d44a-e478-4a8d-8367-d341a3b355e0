package com.senox.user.api.clients;


import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.AuthCredentialsSearchVo;
import com.senox.user.vo.AuthCredentialsVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.user.api.AuthCredentialsUrl.*;

/**
 * <AUTHOR>
 * @date 2023-8-29
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface AuthCredentialsClient {


    /**
     * 添加凭证
     *
     * @param userId 用户id
     */
    @GetMapping(AUTH_CREDENTIALS_ADD)
    void add(@PathVariable Long userId);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    @PostMapping(AUTH_CREDENTIALS_LIST)
    List<AuthCredentialsVo> list(@RequestBody AuthCredentialsSearchVo searchVo);

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    @PostMapping(AUTH_CREDENTIALS_LIST_PAGE)
    PageResult<AuthCredentialsVo> listPage(@RequestBody AuthCredentialsSearchVo searchVo);


    /**
     * 查询身份验证凭证
     *
     * @return 查询到的身份验证凭证
     */
    @GetMapping(AUTH_CREDENTIALS_GET_BY_APP_KEY)
    AuthCredentialsVo getByAppKey();

    /**
     * 获取用户
     *
     * @param appKey appKey
     * @return 用户
     */
    @GetMapping(AUTH_CREDENTIALS_GET_USER)
    AdminUserDto getUser(@PathVariable String appKey);
}
