package com.senox.user.api.clients;

import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.CompanyVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 10:35
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface CompanyClient {

    /**
     * 添加企业
     * @param company
     * @return
     */
    @PostMapping(UserServiceUrl.COMPANY_ADD)
    Long addCompany(@RequestBody CompanyVo company);

    /**
     * 更新企业
     * @param company
     */
    @PostMapping(UserServiceUrl.COMPANY_UPDATE)
    void updateCompany(@RequestBody CompanyVo company);

    /**
     * 根据id获取企业
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.COMPANY_GET)
    CompanyVo getCompany(@PathVariable Long id);

    /**
     * 企业列表
     * @return
     */
    @PostMapping(UserServiceUrl.COMPANY_LIST)
    List<CompanyVo> listCompany();
}
