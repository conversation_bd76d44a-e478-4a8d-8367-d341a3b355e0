package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.CustomerSearchVo;
import com.senox.user.vo.CustomerVo;
import com.senox.user.vo.IcCardSearchVo;
import com.senox.user.vo.IcCardVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/15 11:24
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface CustomerClient {

    /**
     * 添加客户
     * @param customerVo
     * @return
     */
    @PostMapping(UserServiceUrl.CUSTOMER_ADD)
    Long addCustomer(@RequestBody CustomerVo customerVo);

    /**
     * 更新客户
     * @param customerVo
     */
    @PostMapping(UserServiceUrl.CUSTOMER_UPDATE)
    void updateCustomer(@RequestBody CustomerVo customerVo);

    /**
     * 根据id获取客户信息
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.CUSTOMER_GET_BY_ID)
    CustomerVo getCustomer(@PathVariable Long id);

    /**
     * 根据证件号获取客户信息
     * @param idcard
     * @return
     */
    @GetMapping(UserServiceUrl.CUSTOMER_GET_BY_IDCARD)
    CustomerVo getCustomer(@RequestParam String idcard);

    /**
     * 根据客户编号获取客户信息
     * @param serialNo
     * @return
     */
    @GetMapping(UserServiceUrl.CUSTOMER_GET_BY_SERIAL)
    CustomerVo findBySerial(@RequestParam String serialNo);

    /**
     * 客户列表
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.CUSTOMER_LIST)
    PageResult<CustomerVo> listCustomerPage(@RequestBody CustomerSearchVo searchVo);

    /**
     * 客户列表（无分页）
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.CUSTOMER_LIST_NO_PAGE)
    List<CustomerVo> listCustomerNoPage(@RequestBody CustomerSearchVo searchVo);

    /**
     * IC卡列表
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.IC_CARD_LIST)
    PageResult<IcCardVo> listIcCardPage(@RequestBody IcCardSearchVo searchVo);
}
