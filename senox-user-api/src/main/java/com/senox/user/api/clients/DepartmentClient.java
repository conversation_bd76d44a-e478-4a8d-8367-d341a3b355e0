package com.senox.user.api.clients;

import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.DepartmentNode;
import com.senox.user.vo.DepartmentVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/15 13:56
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface DepartmentClient {

    /**
     * 添加部门
     * @param department
     * @return
     */
    @PostMapping(UserServiceUrl.DEPARTMENT_ADD)
    Long addDepartment(@RequestBody DepartmentVo department);

    /**
     * 更新部门
     * @param department
     */
    @PostMapping(UserServiceUrl.DEPARTMENT_UPDATE)
    void updateDepartment(@RequestBody DepartmentVo department);

    /**
     * 获取部门详细
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.DEPARTMENT_GET)
    DepartmentVo getDepartment(@PathVariable Long id);

    /**
     * 部门列表
     * @param parentId
     * @return
     */
    @GetMapping(UserServiceUrl.DEPARTMENT_LIST)
    List<DepartmentVo> listDepartment(@PathVariable Long parentId);

    /**
     * 部门列表
     * @return
     */
    @PostMapping(UserServiceUrl.DEPARTMENT_LIST_ALL)
    List<DepartmentVo> listDepartments();

    /**
     * 部门树
     * @return
     */
    @PostMapping(UserServiceUrl.DEPARTMENT_TREE)
    List<DepartmentNode> listDepartmentTree();
}
