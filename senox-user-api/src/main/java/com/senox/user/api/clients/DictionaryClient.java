package com.senox.user.api.clients;

import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.AreaVo;
import com.senox.user.vo.ProfessionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/14 15:26
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface DictionaryClient {

    /**
     * 添加省/市/区
     * @param areaVo
     * @return
     */
    @PostMapping(UserServiceUrl.AREA_ADD)
    Long addArea(@RequestBody AreaVo areaVo);

    /**
     * 更新省/市/区
     * @param areaVo
     */
    @PostMapping(UserServiceUrl.AREA_UPDATE)
    void updateArea(@RequestBody AreaVo areaVo);

    /**
     * 获取省/市/区信息
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.AREA_GET)
    AreaVo getArea(@PathVariable Long id);

    /**
     * 省/直辖市列表
     * @return
     */
    @PostMapping(UserServiceUrl.AREA_PROVINCE_LIST)
    List<AreaVo> listProvinces();

    /**
     * 市列表
     * @param parentId
     * @return
     */
    @PostMapping(UserServiceUrl.AREA_CITY_LIST)
    List<AreaVo> listCities(@PathVariable Long parentId);

    /**
     * 添加行业
     * @param professionVo
     * @return
     */
    @PostMapping(UserServiceUrl.PROFESSION_ADD)
    Long addProfession(@RequestBody ProfessionVo professionVo);

    /**
     * 更新行业
     * @param professionVo
     */
    @PostMapping(UserServiceUrl.PROFESSION_UPDATE)
    void updateProfession(@RequestBody ProfessionVo professionVo);

    /**
     * 获取行业信息
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.PROFESSION_GET)
    ProfessionVo getProfession(@PathVariable Long id);

    /**
     * 行业列表
     * @return
     */
    @PostMapping(UserServiceUrl.PROFESSION_LIST)
    List<ProfessionVo> listProfessions();
}
