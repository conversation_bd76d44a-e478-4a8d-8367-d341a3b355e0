package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.DiningInformationSearchVo;
import com.senox.user.vo.DiningInformationVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface DiningInformationClient {

    @PostMapping(UserServiceUrl.DINING_INFORMATION_ADD_BATCH)
    void addBatchDiningInformation(@RequestBody List<DiningInformationVo> diningInformationVoList);

    @PostMapping(UserServiceUrl.DINING_INFORMATION_ADD)
    void addDiningInformation(@RequestBody DiningInformationVo diningInformationVo);

    @PostMapping(UserServiceUrl.DINING_INFORMATION_UPDATE)
    void updateDiningInformation(@RequestBody DiningInformationVo diningInformationVo);

    @PostMapping(UserServiceUrl.DINING_INFORMATION_DELETE)
    void deleteDiningInformation(@PathVariable Long id);

    @PostMapping(UserServiceUrl.DINING_INFORMATION_LIST)
    PageResult<DiningInformationVo> listDiningInformation(@RequestBody DiningInformationSearchVo search);

    @GetMapping(UserServiceUrl.DINING_INFORMATION_GET)
    DiningInformationVo getDiningInformation(@PathVariable Long id);
}
