package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:46
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface EmployeeClient {

    /**
     * 新增员工
     * @param employee
     * @return
     */
    @PostMapping(UserServiceUrl.EMPLOYEE_ADD)
    Long addEmployee(@RequestBody EmployeeVo employee);

    /**
     * 更新员工
     * @param employee
     */
    @PostMapping(UserServiceUrl.EMPLOYEE_UPDATE)
    void updateEmployee(@RequestBody EmployeeVo employee);

    /**
     * 根据id获取员工
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.EMPLOYEE_GET)
    EmployeeVo findById(@PathVariable Long id);

    /**
     * 员工列表
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.EMPLOYEE_LIST)
    PageResult<EmployeeVo> listEmployeePage(@RequestBody EmployeeSearchVo searchVo);

    /**
     * 报餐列表
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.BOOKING_MEAL_LIST)
    PageResult<BookingMealVo> listBookingPage(@RequestBody BookingMealSearchVo searchVo);

    /**
     * 报餐日报表
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.BOOKING_MEAL_DAY_REPORT)
    PageResult<BookingMealCompanyDayReportVo> listBookingCompanyDayReportPage(@RequestBody BookingMealDayReportSearchVo searchVo);

    /**
     * 报餐详情
     * @param mealDate
     * @return
     */
    @GetMapping(UserServiceUrl.BOOKING_MEAL_DAY_REPORT_DETAIL)
    List<BookingMealCompanyDayReportVo> listBookingCompanyDayReportDetail(@RequestParam String mealDate);

    /**
     * 新增/保存假期
     * @param holidays
     */
    @PostMapping(UserServiceUrl.HOLIDAY_SAVE)
    void saveHolidays(@RequestBody List<HolidayVo> holidays);

    /**
     * 删除假期
     * @param dateList
     */
    @PostMapping(UserServiceUrl.HOLIDAY_DELETE)
    void deleteHolidays(@RequestBody List<LocalDate> dateList);

    /**
     * 假期列表
     * @return
     */
    @PostMapping(UserServiceUrl.HOLIDAY_LIST)
    List<HolidayVo> listHoliday(@RequestBody HolidaySearchVo searchVo);
}
