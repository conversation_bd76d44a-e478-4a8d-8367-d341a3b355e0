package com.senox.user.api.clients;

import com.senox.user.api.UserServiceUrl;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2024/9/12 17:24
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface FeignInvokeClient {

    /**
     * 获取feignToken
     * @return
     */
    @GetMapping(UserServiceUrl.FEIGN_INVOKE_GET)
    String getFeignToken();
}
