package com.senox.user.api.clients;

import com.senox.common.vo.AuditVo;
import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.MerchantAuthApplyEditVo;
import com.senox.user.vo.MerchantAuthApplyListVo;
import com.senox.user.vo.MerchantAuthApplySearchVo;
import com.senox.user.vo.MerchantAuthApplyVo;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

import static com.senox.user.api.MerchantServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface MerchantClient {

    /**
     * 添加商户
     *
     * @param merchant 商户
     * @return id
     */
    @PostMapping(MERCHANT_ADD)
    Long add(@RequestBody MerchantVo merchant);

    /**
     * 批量新增商户，返回已存在的商户列表
     * @param list
     * @return
     */
    @PostMapping(MERCHANT_BATCH_ADD)
    List<MerchantVo> batchAddMerchant(@RequestBody List<MerchantVo> list);

    /**
     * 更新商户
     *
     * @param merchant 商户
     */
    @PostMapping(MERCHANT_UPDATE)
    void update(@RequestBody MerchantVo merchant);

    /**
     * 新增或保存商户
     * @param merchant
     * @return
     */
    @PostMapping(MERCHANT_SAVE)
    Long save(@RequestBody MerchantVo merchant);

    /**
     * 删除商户
     * @param id
     */
    @PostMapping(MERCHANT_DELETE)
    void delete(@RequestParam Long id);

    /**
     * 根据id查询商户
     *
     * @param id id
     * @return 返回查询到的商户
     */
    @GetMapping(MERCHANT_GET)
    MerchantVo findById(@PathVariable Long id);

    /**
     * 根据商户名查找商户
     * @param name
     * @return
     */
    @GetMapping(MERCHANT_GET_BY_NAME)
    MerchantVo findByName(@RequestParam String name);

    /**
     * 根据冷藏客户编号查找商户
     * @param rcSerial
     * @return
     */
    @GetMapping(MERCHANT_GET_BY_RC)
    MerchantVo findByRc(@RequestParam String rcSerial);

    /**
     * 根据冷藏编号集合查询商户
     * @param rcSerialList
     * @return
     */
    @PostMapping(MERCHANT_GET_BY_RC_LIST)
    List<MerchantVo> findByRcSerialList(@RequestBody List<String> rcSerialList);

    /**
     * 根据手机号查询商户
     * @param contact
     * @return
     */
    @GetMapping(MERCHANT_GET_BY_CONTACT)
    List<MerchantVo> findByContact(@RequestParam String contact);

    /**
     * 商户列表
     * @param search
     * @return
     */
    @PostMapping(MERCHANT_LIST)
    List<MerchantVo> list(@RequestBody MerchantSearchVo search);

    /**
     * 商户列表
     * @param search
     * @return
     */
    @PostMapping(MERCHANT_VIEW_LIST)
    List<MerchantVo> listView(@RequestBody MerchantSearchVo search);

    /**
     * 商户列表页
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    @PostMapping(MERCHANT_PAGE)
    PageResult<MerchantVo> listPage(@RequestBody MerchantSearchVo search);

    /**
     * 新增权限申请
     *
     * @param apply 申请参数
     * @return
     */
    @PostMapping(MERCHANT_AUTH_APPLY_ADD)
    Long addApply(@RequestBody MerchantAuthApplyEditVo apply);

    /**
     * 修改权限申请
     *
     * @param apply 申请参数
     */
    @PostMapping(MERCHANT_AUTH_APPLY_UPDATE)
    void updateApply(@RequestBody MerchantAuthApplyEditVo apply);

    /**
     * 删除权限申请
     * @param id
     */
    @PostMapping(MERCHANT_AUTH_APPLY_DELETE)
    void deleteApply(@PathVariable Long id);

    /**
     * 权限申请审批
     * @param audit 审批
     * @return
     */
    @PostMapping(MERCHANT_AUTH_APPLY_AUDIT)
    MerchantVo auditApply(@RequestBody AuditVo audit);

    /**
     * 根据 id 查找权限申请
     * @param id
     * @return
     */
    @GetMapping(MERCHANT_AUTH_APPLY_GET)
    MerchantAuthApplyVo findApplyById(@PathVariable Long id);

    /**
     * 申请记录合计
     * @param search
     * @return
     */
    @PostMapping(MERCHANT_APPLY_COUNT)
    int countApply(@RequestBody MerchantAuthApplySearchVo search);

    /**
     * 申请列表
     * @param search
     * @return
     */
    @PostMapping(MERCHANT_APPLY_LIST)
    List<MerchantAuthApplyListVo> listApply(@RequestBody MerchantAuthApplySearchVo search);

    /**
     * 申请列表页
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    @PostMapping(MERCHANT_APPLY_PAGE)
    PageResult<MerchantAuthApplyListVo> listApplyPage(@RequestBody MerchantAuthApplySearchVo search);

    /**
     * 待审核数量
     * @return
     */
    @GetMapping(MERCHANT_APPLY_AUDIT_COUNT)
    Integer auditCount();

    /**
     * 更新收费标准
     *
     * @param ids       id集
     * @param chargesId 收费标准id
     * @param fullData  true:全量;false:增量
     */
    @GetMapping(MERCHANT_UPDATE_CHARGES)
    void updateCharges(@RequestParam Collection<Long> ids, @RequestParam Long chargesId, @RequestParam Boolean fullData);

}
