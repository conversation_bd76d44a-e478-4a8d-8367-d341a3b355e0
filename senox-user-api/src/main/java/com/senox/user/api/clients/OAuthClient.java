package com.senox.user.api.clients;

import com.senox.context.AdminUserDto;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.authcredentials.dto.AccessTokenResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import static com.senox.user.api.OAuthServiceUrl.OAUTH_ACCESS_TOKEN;
import static com.senox.user.api.OAuthServiceUrl.OAUTH_USER_FROM_ACCESS_TOKEN;

/**
 * <AUTHOR>
 * @date 2024-2-23
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface OAuthClient {

    /**
     * 访问令牌
     *
     * @param appKey    appKey
     * @param appSecret appSecret
     * @return 返回带超时时间的访问令牌
     */
    @GetMapping(OAUTH_ACCESS_TOKEN)
    AccessTokenResultDto accessToken(@RequestParam String appKey, @RequestParam String appSecret);

    /**
     * 访问令牌用户
     *
     * @param accessToken 访问令牌
     * @return 返回用户信息
     */
    @GetMapping(OAUTH_USER_FROM_ACCESS_TOKEN)
    AdminUserDto userFromAccessToken(@RequestParam String accessToken);

}
