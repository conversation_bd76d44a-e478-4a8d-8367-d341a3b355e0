package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.PrizeServiceUrl;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/4/15 14:28
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface PrizeClient {

    /**
     * 添加抽奖奖品
      * @param prizeVo
     * @return
     */
    @PostMapping(PrizeServiceUrl.PRIZE_ADD)
    Long addPrize(@RequestBody PrizeVo prizeVo);

    /**
     * 更新抽奖奖品
     * @param prizeVo
     */
    @PostMapping(PrizeServiceUrl.PRIZE_UPDATE)
    void updatePrize(@RequestBody PrizeVo prizeVo);

    /**
     * 根据id获取抽奖奖品
     * @param id
     * @return
     */
    @GetMapping(PrizeServiceUrl.PRIZE_GET)
    PrizeVo findById(@PathVariable Long id);

    /**
     * 根据id删除抽奖奖品
     * @param id
     */
    @PostMapping(PrizeServiceUrl.PRIZE_DELETE)
    void deleteById(@PathVariable Long id);

    /**
     * 抽奖奖品分页
     * @param searchVo
     * @return
     */
    @PostMapping(PrizeServiceUrl.PRIZE_PAGE)
    PageResult<PrizeVo> pagePrize(@RequestBody PrizeSearchVo searchVo);

    /**
     * 抽奖
     * @param activityId
     * @param openid
     * @return
     */
    @GetMapping(PrizeServiceUrl.PRIZE_DRAW)
    PrizeRecordsVo drawPrize(@PathVariable Long activityId, @RequestParam String openid);

    /**
     * 增加抽奖次数
     * @param drawNumberVo
     */
    @PostMapping(PrizeServiceUrl.PRIZE_INCREASE_DRAW_NUMBER)
    void increaseDrawNumber(@RequestBody PrizeDrawNumberVo drawNumberVo);

    /**
     * 查询抽奖次数
     * @param drawNumberVo
     * @return
     */
    @PostMapping(PrizeServiceUrl.PRIZE_DRAW_NUMBER_COUNT)
    int countPrizeDrawNumber(@RequestBody PrizeDrawNumberVo drawNumberVo);

    /**
     * 用户可用次数
     * @param activityId
     * @param openid
     * @return
     */
    @GetMapping(PrizeServiceUrl.PRIZE_AVAILABLE_NUMBER)
    Integer availableNumber(@PathVariable Long activityId, @RequestParam String openid);

    /**
     * 根据id获取抽奖记录
     * @param id
     * @return
     */
    @GetMapping(PrizeServiceUrl.PRIZE_RECORDS_GET)
    PrizeRecordsVo findRecordsById(@PathVariable Long id);

    /**
     * 根据uuid获取抽奖记录
     * @param uuid
     * @return
     */
    @GetMapping(PrizeServiceUrl.PRIZE_RECORDS_BY_UUID)
    PrizeRecordsVo findByUuid(@RequestParam String uuid);

    /**
     * 抽奖记录分页
     * @param searchVo
     * @return
     */
    @PostMapping(PrizeServiceUrl.PRIZE_RECORDS_PAGE)
    PageResult<PrizeRecordsVo> pageRecords(@RequestBody PrizeRecordsSearchVo searchVo);

    /**
     * 根据uuid兑奖
     * @param uuid
     */
    @GetMapping(PrizeServiceUrl.PRIZE_VERIFY_PRIZE)
    void verifyPrize(@RequestParam String uuid);
}
