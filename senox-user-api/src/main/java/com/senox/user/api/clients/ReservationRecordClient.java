package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.ReservationRecordServiceUrl;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.ReservationRecordSearchVo;
import com.senox.user.vo.ReservationRecordVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2023/12/27 18:22
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface ReservationRecordClient {

    /**
     * 添加预约记录
     * @param recordVo
     * @return
     */
    @PostMapping(ReservationRecordServiceUrl.RESERVATION_RECORD_ADD)
    Long addReservationRecord(@RequestBody ReservationRecordVo recordVo);

    /**
     * 更新预约记录
     * @param recordVo
     */
    @PostMapping(ReservationRecordServiceUrl.RESERVATION_RECORD_UPDATE)
    void updateParkingRecord(@RequestBody ReservationRecordVo recordVo);

    /**
     * 根据id获取预约记录
     * @param id
     * @return
     */
    @GetMapping(ReservationRecordServiceUrl.RESERVATION_RECORD_GET)
    ReservationRecordVo findById(@PathVariable Long id);

    /**
     * 预约记录列表
     * @param searchVo
     * @return
     */
    @PostMapping(ReservationRecordServiceUrl.RESERVATION_RECORD_LIST)
    PageResult<ReservationRecordVo> page(@RequestBody ReservationRecordSearchVo searchVo);

    /**
     * 预约记录同行人数合计
     * @param searchVo
     * @return
     */
    @PostMapping(ReservationRecordServiceUrl.RESERVATION_RECORD_SUM)
    ReservationRecordVo sumReservationRecord(@RequestBody ReservationRecordSearchVo searchVo);

}
