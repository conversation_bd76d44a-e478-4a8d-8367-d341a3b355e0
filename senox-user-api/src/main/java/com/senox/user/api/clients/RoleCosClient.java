package com.senox.user.api.clients;

import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.CosVo;
import com.senox.user.vo.RoleVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/28 10:58
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface RoleCosClient {

    /**
     * 添加角色
     * @param role
     * @return
     */
    @PostMapping(UserServiceUrl.ROLE_ADD)
    Long addRole(@RequestBody RoleVo role);

    /**
     * 更新橘色
     * @param role
     */
    @PostMapping(UserServiceUrl.ROLE_UPDATE)
    void updateRole(@RequestBody RoleVo role);

    /**
     * 删除角色
     * @param id
     */
    @PostMapping(UserServiceUrl.ROLE_DELETE)
    void deleteRole(@PathVariable Long id);

    /**
     * 根据id获取角色
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.ROLE_GET)
    RoleVo getRole(@PathVariable Long id);

    /**
     * 角色列表
     * @return
     */
    @PostMapping(UserServiceUrl.ROLE_LIST)
    List<RoleVo> listRole();

    /**
     * 添加权限项
     * @param cos
     * @return
     */
    @PostMapping(UserServiceUrl.COS_ADD)
    Long addCos(@RequestBody CosVo cos);

    /**
     * 更新权限项
     * @param cos
     */
    @PostMapping(UserServiceUrl.COS_UPDATE)
    void updateCos(@RequestBody CosVo cos);

    /**
     * 删除权限项
     * @param id
     */
    @PostMapping(UserServiceUrl.COS_DELETE)
    void deleteCos(@PathVariable Long id);

    /**
     * 获取权限项
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.COS_GET)
    CosVo getCos(@PathVariable Long id);

    /**
     * 权限项列表
     * @return
     */
    @PostMapping(UserServiceUrl.COS_LIST)
    List<CosVo> listCos();
}
