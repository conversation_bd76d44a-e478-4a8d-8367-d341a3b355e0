package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.api.ActivityServiceUrl;
import com.senox.user.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/17 9:07
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface VoteClient {

    /**
     * 添加投票分类
     * @param categoryVo
     */
    @PostMapping(ActivityServiceUrl.VOTE_CATEGORY_SAVE)
    void saveVoteCategory(@RequestBody VoteCategoryVo categoryVo);

    /**
     * 根据id获取投票分类
     * @param id
     * @return
     */
    @GetMapping(ActivityServiceUrl.VOTE_CATEGORY_GET)
    VoteCategoryVo findCategoryById(@PathVariable Long id);

    /**
     * 根据id删除投票类别
     * @param id
     */
    @GetMapping(ActivityServiceUrl.VOTE_CATEGORY_DELETE)
    void deleteVoteCategoryById(@PathVariable Long id);

    /**
     * 投票类别分页
     * @param searchVo
     * @return
     */
    @PostMapping(ActivityServiceUrl.VOTE_CATEGORY_PAGE)
    PageResult<VoteCategoryVo> pageCategoryResult(@RequestBody VoteCategorySearchVo searchVo);

    /**
     * 新增投票资源
     * @param resourcesVo
     */
    @PostMapping(ActivityServiceUrl.VOTE_RESOURCES_SAVE)
    void saveVoteResources(@RequestBody VoteResourcesVo resourcesVo);

    /**
     * 根据id获取投票资源
      * @param id
     * @return
     */
    @GetMapping(ActivityServiceUrl.VOTE_RESOURCES_GET)
    VoteResourcesVo findResourcesById(@PathVariable Long id);

    /**
     * 根据id删除投票资源
     * @param id
     */
    @GetMapping(ActivityServiceUrl.VOTE_RESOURCES_DELETE)
    void deleteVoteResourcesById(@PathVariable Long id);

    /**
     * 投票资源分页
     * @param searchVo
     * @return
     */
    @PostMapping(ActivityServiceUrl.VOTE_RESOURCES_PAGE)
    PageResult<VoteResourcesVo> pageResourcesResult(@RequestBody VoteResourcesSearchVo searchVo);

    /**
     * 新增投票记录
     * @param recordsVo
     */
    @PostMapping(ActivityServiceUrl.VOTE_RECORDS_SAVE)
    void saveVoteRecords(@RequestBody VoteRecordsVo recordsVo);

    /**
     * 用户可用次数
     * @param activityId
     * @param openid
     * @return
     */
    @GetMapping(ActivityServiceUrl.VOTE_RECORDS_AVAILABLE_NUMBERS)
    Integer availableNumbers(@PathVariable Long activityId, @RequestParam String openid);
}
