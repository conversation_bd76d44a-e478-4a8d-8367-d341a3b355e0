package com.senox.user.component;

import com.senox.context.AdminUserDto;
import com.senox.user.api.clients.AdminUserClient;
import com.senox.user.api.clients.AuthCredentialsClient;
import com.senox.user.api.clients.OAuthClient;
import com.senox.user.vo.WxUserVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021/1/8 14:47
 */
@Component
public class AdminUserComponent {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserComponent.class);
    @Lazy
    @Resource
    private AdminUserClient adminUserClient;

    @Lazy
    @Resource
    private AuthCredentialsClient authCredentialsClient;

    @Lazy
    @Resource
    private OAuthClient oAuthClient;

    /**
     * 从feignToken中获取用户
     * @param feignToken
     * @return
     */
    public AdminUserDto getUserFromFeignToken(String feignToken) {
        if (feignToken == null || feignToken.length() < 1) {
            return null;
        }
        logger.debug("Get admin from feign token {}", feignToken);
        return adminUserClient.checkFeignToken(feignToken);
    }

    /**
     * 从token中获取用户
     * @param token
     * @return
     */
    @Cacheable(value = "ADMIN_USER", key = "#token", unless = "#result == null")
    public AdminUserDto getUserFromToken(String token) {
        if (token == null || token.length() < 1) {
            return null;
        }
        logger.debug("Get admin from token {}", token);
        return adminUserClient.checkToken(token);
    }

    @CacheEvict(value = "ADMIN_USER", key = "#token")
    public void invalidToken(String token) {
        adminUserClient.invalidToken(token);
    }

    @Cacheable(value = "ADMIN_WECHAT_USER", key = "#appId + '_' + #token", unless = "#result == null")
    public AdminUserDto getUserFromWechatToken(String appId, String token) {
        if (token == null || token.length() < 1) {
            return null;
        }
        logger.debug("Get admin from wechat appId: {}, token {}", appId, token);
        return adminUserClient.checkWechatToken(appId, token);
    }

    @Cacheable(value = "WECHAT_USER", key = "#appId + '_' + #token", unless = "#result == null")
    public WxUserVo getWxUserFromWechatToken(String appId, String token) {
        if (token == null || token.length() < 1) {
            return null;
        }
        logger.debug("Get user from wechat token {}", token);
        return adminUserClient.checkWechatTk(appId, token);
    }

    @Cacheable(value = "AUTH_CREDENTIALS", key = "#credentials", unless = "#result == null")
    public AdminUserDto getUserFromAuthCredentials(String credentials) {
        if (credentials == null || credentials.length() < 1) {
            return null;
        }
        logger.debug("Get user from credentials {}", credentials);
        return authCredentialsClient.getUser(credentials);
    }

    public AdminUserDto getUserFromAuthAccessToken(String accessToken) {
        if (accessToken == null || accessToken.isEmpty()) {
            return null;
        }
        logger.debug("Get user from access token {}", accessToken);
        return oAuthClient.userFromAccessToken(accessToken);
    }
}
