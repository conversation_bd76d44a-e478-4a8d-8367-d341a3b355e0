package com.senox.user.component;

import com.senox.user.api.clients.FeignInvokeClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/12 17:25
 */
@Component
public class FeignInvokeComponent {

    @Lazy
    @Resource
    private FeignInvokeClient feignInvokeClient;

    public String getFeignToken() {
        return feignInvokeClient.getFeignToken();
    }
}
