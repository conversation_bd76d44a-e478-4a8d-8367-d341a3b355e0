package com.senox.user.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @Date 2021/1/11 11:09
 */
@Configuration
public class AdminUserClientConfigure {

    @Bean
    public AdminUserClientInterceptor adminUserClientInterceptor() {
        return new AdminUserClientInterceptor();
    }

    @Bean
    @Profile("!test")
    public AdminUserClientFilter adminUserClientFilter() {
        return new AdminUserClientFilter();
    }
}
