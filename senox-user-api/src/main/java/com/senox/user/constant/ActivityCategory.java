package com.senox.user.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/10 10:25
 */
@Getter
public enum ActivityCategory {

    VOTE(0, "投票"),
    PRIZE_DRAW(1, "抽奖");

    private final int number;
    private final String name;

    ActivityCategory(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static ActivityCategory fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (ActivityCategory item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static ActivityCategory fromName(String name) {
        if (null == name) {
            return null;
        }
        for (ActivityCategory item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}
