package com.senox.user.constant;

/**
 * 地区类别
 * <AUTHOR>
 * @Date 2020/12/14 16:12
 */
public enum AreaCategory {

    /**
     * 省
     */
    PROVINCE(1),
    /**
     * 直辖市
     */
    CITY_DIRECTLY(2),
    /**
     * 市
     */
    CITY(3),
    ;

    private final int value;

    AreaCategory(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static AreaCategory fromValue(Integer value) {
        if (value != null) {
            for (AreaCategory item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
