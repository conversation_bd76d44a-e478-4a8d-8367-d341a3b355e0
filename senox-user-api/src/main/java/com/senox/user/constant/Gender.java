package com.senox.user.constant;

/**
 * 性别
 * <AUTHOR>
 * @Date 2020/12/28 17:28
 */
public enum Gender {

    /**
     * 男
     */
    MALE(1, "男"),
    /**
     * 女
     */
    FEMALE(2, "女"),
    /**
     * 其他
     */
    OTHERS(3, "其他"),
    ;


    private final int value;
    private final String name;

    Gender(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static Gender fromValue(Integer value) {
        if (value != null) {
            for (Gender item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
