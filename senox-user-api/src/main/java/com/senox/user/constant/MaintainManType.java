package com.senox.user.constant;


/**
 * <AUTHOR>
 * @date 2023/8/2 11:58
 */
public enum MaintainManType {
    /**
     * 员工
     */
    NORMAL(0,"员工"),
    /**
     * 维修员
     */
    MAINTAIN_MAN(1,"维修员"),
    /**
     * 维修主管
     */
    MAINTAIN_MANAGER(2,"维修主管")
    ;

    private final int value;

    private final String name;

    MaintainManType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static MaintainManType fromValue(int value) {
        for (MaintainManType item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }
}
