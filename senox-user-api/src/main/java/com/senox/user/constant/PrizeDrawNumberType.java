package com.senox.user.constant;

/**
 * <AUTHOR>
 * @date 2025/4/15 9:29
 */
public enum PrizeDrawNumberType {

    /**
     * 关注
     */
    FOLLOW(0,"关注"),
    /**
     * 转发
     */
    FORWARD(1,"转发");


    private final int value;
    private final String name;

    PrizeDrawNumberType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static PrizeDrawNumberType fromValue(Integer value) {
        if (value != null) {
            for (PrizeDrawNumberType item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
