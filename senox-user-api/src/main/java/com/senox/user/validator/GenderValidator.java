package com.senox.user.validator;


import com.senox.user.constant.Gender;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 性别类型校验
 * <AUTHOR>
 * @Date 2020/12/29 17:16
 */
public class GenderValidator implements ConstraintValidator<GenderChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(value)) {
            return true;
        }
        Gender gender = Gender.fromValue(value);
        return gender != null;
    }
}
