package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024/10/16 8:53
 */
@ApiModel("活动查询参数")
@Getter
@Setter
public class ActivitySearchVo extends PageRequest {
    private static final long serialVersionUID = 3134106412182989512L;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String name;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 状态(0:初始化;1:生效;2:未生效)
     */
    @ApiModelProperty("状态(0:初始化;1:生效;2:未生效)")
    private Integer status;

    /**
     * @see com.senox.user.constant.ActivityCategory
     * 类别
     */
    @ApiModelProperty("类别（0投票，1抽奖）")
    private Integer category;
}
