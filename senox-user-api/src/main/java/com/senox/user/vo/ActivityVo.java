package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/16 8:41
 */
@Data
@ApiModel("活动")
public class ActivityVo implements Serializable {
    private static final long serialVersionUID = -8237830871568431860L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String name;

    /**
     * UUID
     */
    @ApiModelProperty("UUID")
    private String uuid;

    /**
     * 活动链接
     */
    @ApiModelProperty("url")
    private String url;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 背景图片
     */
    @ApiModelProperty("背景图片")
    private String bgUrl;

    /**
     * 背景颜色
     */
    @ApiModelProperty("背景颜色")
    private String bgColor;

    /**
     * 字体颜色
     */
    @ApiModelProperty("字体颜色")
    private String fontColor;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 分享标题
     */
    @ApiModelProperty("分享标题")
    private String shareTitle;

    /**
     * 分享简介
     */
    @ApiModelProperty("分享简介")
    private String shareBlurb;

    /**
     * 分享图片
     */
    @ApiModelProperty("分享图片")
    private String shareUrl;

    /**
     * 活动简介
     */
    @ApiModelProperty("活动简介")
    private String blurb;

    /**
     * 中止时间
     */
    @ApiModelProperty("中止时间")
    private LocalDateTime stopTime;

    /**
     * 限制数量
     */
    @ApiModelProperty("限制数量")
    private Integer limitNum;

    /**
     * @see com.senox.user.constant.ActivityStatus
     * 状态
     */
    @ApiModelProperty("状态（0初始化，1生效，2未生效）")
    private Integer status;

    /**
     * @see com.senox.user.constant.ActivityCategory
     * 类别
     */
    @ApiModelProperty("类别（0投票，1抽奖）")
    private Integer category;
}
