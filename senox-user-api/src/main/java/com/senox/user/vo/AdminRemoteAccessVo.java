package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/26 9:29
 */
@ApiModel("管理员远程权限")
public class AdminRemoteAccessVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id 不能为空", groups = Add.class)
    private Long adminUserId;

    @ApiModelProperty("用户名")
    private String adminRealName;

    @ApiModelProperty("设备id")
    @NotNull(message = "设备id 不能为空", groups = Add.class)
    private Long deviceId;

    @ApiModelProperty("设备名")
    private String deviceName;

    @ApiModelProperty("设备ip")
    private String deviceIp;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("街道名")
    private String streetName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(Long adminUserId) {
        this.adminUserId = adminUserId;
    }

    public String getAdminRealName() {
        return adminRealName;
    }

    public void setAdminRealName(String adminRealName) {
        this.adminRealName = adminRealName;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public Long getStreetId() {
        return streetId;
    }

    public void setStreetId(Long streetId) {
        this.streetId = streetId;
    }

    public String getStreetName() {
        return streetName;
    }

    public void setStreetName(String streetName) {
        this.streetName = streetName;
    }
}
