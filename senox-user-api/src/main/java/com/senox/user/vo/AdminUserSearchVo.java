package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/12 15:31
 */
@ApiModel("管理员列表查询参数")
public class AdminUserSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = 3168935356256779191L;

    @ApiModelProperty(value = "账号", notes = "模糊匹配")
    private String username;

    @ApiModelProperty(value = "姓名", notes = "模糊匹配")
    private String realName;

    @ApiModelProperty(value = "手机号", notes = "支持前置匹配")
    private String telephone;

    @ApiModelProperty(value = "0：员工，1：维修员，2：维修主管")
    private List<Integer> maintainManType;

    @ApiModelProperty("是否维修主管")
    private Boolean isMaintainManager;

    @Getter
    @ApiModelProperty("部门id")
    private Long departmentId;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public List<Integer> getMaintainManType() {
        return maintainManType;
    }

    public void setMaintainManType(List<Integer> maintainManType) {
        this.maintainManType = maintainManType;
    }

    public Boolean getMaintainManager() {
        return isMaintainManager;
    }

    public void setMaintainManager(Boolean maintainManager) {
        isMaintainManager = maintainManager;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

}
