package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.validator.GenderChecker;
import com.senox.user.validator.TelephoneChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/28 17:26
 */
@ApiModel("管理员用户")
@Data
@ToString
public class AdminUserVo implements Serializable {

    private static final long serialVersionUID = -6167585466390244248L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("用户名")
    @NotBlank(message = "用户名不能为空", groups = Add.class)
    @Length(min = 2, message = "用户名要求至少包含2个字符")
    private String username;

    @ApiModelProperty("姓名")
    @NotBlank(message = "姓名不能为空", groups = Add.class)
    private String realName;

    @ApiModelProperty("密码")
    @NotBlank(message = "密码不能为空", groups = Add.class)
    private String password;

    @ApiModelProperty(value = "性别", notes = "1男；2女；3其他")
    @NotNull(message = "性别不能为空", groups = Add.class)
    @GenderChecker(message = "无效性别")
    private Integer gender;

    @ApiModelProperty("邮箱")
    @Email(message = "无效的邮箱")
    private String email;

    @ApiModelProperty("手机")
    @TelephoneChecker(message = "无效的手机")
    private String telephone;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("部门名")
    private String departmentName;

    @ApiModelProperty("是否收费员")
    private Boolean tollMan;

    @ApiModelProperty("0：员工，1：维修员，2：维修主管")
    private Integer maintainManType;

    @ApiModelProperty("登录跳转路径")
    private String loginPath;

    @ApiModelProperty("系统使用权")
    private Boolean loginable;

    @ApiModelProperty("收据编号")
    private String billSerial;

    @ApiModelProperty("支付终端序列号")
    private String payDeviceSn;

    @ApiModelProperty("角色列表")
    private List<Long> roles;

    @ApiModelProperty("角色编码列表")
    private List<String> roleCodes;

    @ApiModelProperty("部门列表")
    private List<Long> departments;

    @ApiModelProperty(value = "签名", hidden = true)
    private String token;

    @ApiModelProperty(value = "禁用", hidden = true)
    private Boolean disabled;

}
