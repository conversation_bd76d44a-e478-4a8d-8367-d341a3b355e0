package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.validator.AreaCategoryChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/30 14:53
 */
@ApiModel("地区")
public class AreaVo implements Serializable {

    private static final long serialVersionUID = 2531425226938999093L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("编号")
    @NotBlank(message = "编号名不能为空", groups = Add.class)
    private String serialNo;

    @ApiModelProperty("地区名")
    @NotBlank(message = "地区名不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("地区简称")
    private String briefName;

    @ApiModelProperty(value = "地区类型", notes = "1省；2直辖市；3市")
    @AreaCategoryChecker(message = "无效的地区类型")
    @NotNull(message = "地区类型不能为空", groups = Add.class)
    private Integer category;

    @ApiModelProperty(value = "父地区id")
    @Min(value = 0, message = "父地区id只能为自然数")
    private Long parentId;

    @ApiModelProperty(value = "是否禁用", hidden = true)
    private Boolean disabled;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBriefName() {
        return briefName;
    }

    public void setBriefName(String briefName) {
        this.briefName = briefName;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "AreaVo{"
                + "id=" + id
                + ", serialNo='" + serialNo + '\''
                + ", name='" + name + '\''
                + ", briefName='" + briefName + '\''
                + ", category=" + category
                + ", parentId=" + parentId
                + ", disabled=" + disabled
                + '}';
    }
}
