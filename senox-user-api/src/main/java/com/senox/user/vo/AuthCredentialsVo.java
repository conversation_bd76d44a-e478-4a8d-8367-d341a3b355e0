package com.senox.user.vo;

import com.senox.context.AdminUserDto;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-8-29
 */
@Setter
@Getter
@ApiModel("身份验证凭证")
public class AuthCredentialsVo implements Serializable {
    private static final long serialVersionUID = 7815440409042359799L;

    /**
     * 公钥
     */
    private String appKey;

    /**
     * 私钥
     */
    private String appSecret;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户
     */
    private AdminUserDto userDto;
}
