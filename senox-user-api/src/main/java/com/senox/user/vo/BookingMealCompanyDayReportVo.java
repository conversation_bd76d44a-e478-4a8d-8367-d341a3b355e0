package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/4/16 15:15
 */
@ApiModel("报餐公司日报")
public class BookingMealCompanyDayReportVo implements Serializable {

    private static final long serialVersionUID = -8261895261589498117L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("就餐日期")
    private LocalDate mealDate;

    @ApiModelProperty("企业")
    private String company;

    @ApiModelProperty("订餐人数")
    private Integer bookedCount;

    @ApiModelProperty("不订餐人数")
    private Integer unbookedCount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getMealDate() {
        return mealDate;
    }

    public void setMealDate(LocalDate mealDate) {
        this.mealDate = mealDate;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Integer getBookedCount() {
        return bookedCount;
    }

    public void setBookedCount(Integer bookedCount) {
        this.bookedCount = bookedCount;
    }

    public Integer getUnbookedCount() {
        return unbookedCount;
    }

    public void setUnbookedCount(Integer unbookedCount) {
        this.unbookedCount = unbookedCount;
    }
}
