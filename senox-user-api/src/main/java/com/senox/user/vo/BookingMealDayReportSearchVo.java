package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/16 12:01
 */
@ApiModel("报餐日报查询参数")
public class BookingMealDayReportSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = -7923496804880578495L;


    @ApiModelProperty("公司")
    private String company;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("日期列表")
    private List<LocalDate> dateList;

    @ApiModelProperty(value = "代理的企业列表", hidden = true)
    private List<CompanyVo> delegateCompanies;


    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public List<LocalDate> getDateList() {
        return dateList;
    }

    public void setDateList(List<LocalDate> dateList) {
        this.dateList = dateList;
    }

    public List<CompanyVo> getDelegateCompanies() {
        return delegateCompanies;
    }

    public void setDelegateCompanies(List<CompanyVo> delegateCompanies) {
        this.delegateCompanies = delegateCompanies;
    }
}
