package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/4/6 9:41
 */
@ApiModel("订餐查询参数")
public class BookingMealSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = 3477830001953798203L;

    @ApiModelProperty("企业")
    private String company;

    @ApiModelProperty("员工")
    private String employee;

    @ApiModelProperty("预定时间起")
    private LocalDate bookingDateStart;

    @ApiModelProperty("预定时间止")
    private LocalDate bookingDateEnd;

    @ApiModelProperty("订餐")
    private Boolean mealBook;


    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getEmployee() {
        return employee;
    }

    public void setEmployee(String employee) {
        this.employee = employee;
    }

    public LocalDate getBookingDateStart() {
        return bookingDateStart;
    }

    public void setBookingDateStart(LocalDate bookingDateStart) {
        this.bookingDateStart = bookingDateStart;
    }

    public LocalDate getBookingDateEnd() {
        return bookingDateEnd;
    }

    public void setBookingDateEnd(LocalDate bookingDateEnd) {
        this.bookingDateEnd = bookingDateEnd;
    }

    public Boolean getMealBook() {
        return mealBook;
    }

    public void setMealBook(Boolean mealBook) {
        this.mealBook = mealBook;
    }
}
