package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/4/6 10:32
 */
@ApiModel("报餐信息")
public class BookingMealVo implements Serializable {

    private static final long serialVersionUID = 2202483537662007458L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户微信id")
    private String openid;

    @ApiModelProperty("企业")
    private String company;

    @ApiModelProperty("员工")
    private String employee;

    @ApiModelProperty("代报餐企业")
    private Long delegateCompany;

    @ApiModelProperty("就餐日期")
    private LocalDate mealDate;

    @ApiModelProperty("订餐人数")
    private Integer mealBook;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getEmployee() {
        return employee;
    }

    public void setEmployee(String employee) {
        this.employee = employee;
    }

    public LocalDate getMealDate() {
        return mealDate;
    }

    public void setMealDate(LocalDate mealDate) {
        this.mealDate = mealDate;
    }

    public Long getDelegateCompany() {
        return delegateCompany;
    }

    public void setDelegateCompany(Long delegateCompany) {
        this.delegateCompany = delegateCompany;
    }

    public Integer getMealBook() {
        return mealBook;
    }

    public void setMealBook(Integer mealBook) {
        this.mealBook = mealBook;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }
}
