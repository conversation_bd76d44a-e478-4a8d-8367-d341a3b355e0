package com.senox.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:07
 */
@Getter
@Setter
@ToString
public class BusinessCategoryVo {

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的编码")
    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("排序号")
    private Integer orderNum;
}
