package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/1/27 13:46
 */
@ApiModel("权限项")
public class CosVo implements Serializable {

    private static final long serialVersionUID = 5768737327489766053L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("权限项代码")
    @NotBlank(message = "权限项代码不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("权限项名")
    @NotBlank(message = "权限项名不能为空", groups = Add.class)
    private String displayName;

    @ApiModelProperty("权限路径")
    private String url;

    @ApiModelProperty("父权限项")
    @Min(value = 0, message = "无效的父权限项")
    private Long parentId;

    @ApiModelProperty("排序号")
    private Integer orderNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }
}
