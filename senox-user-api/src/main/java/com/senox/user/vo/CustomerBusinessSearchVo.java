package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2024/6/28 10:54
 */
@ApiModel("客户经营户查询参数")
@Getter
@Setter
public class CustomerBusinessSearchVo extends PageRequest {
    private static final long serialVersionUID = -3608084077212366724L;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

    /**
     * 招牌号
     */
    @ApiModelProperty("招牌号")
    private String signNo;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contract;

    /**
     * 营业执照全称
     */
    @ApiModelProperty("营业执照全称")
    private String businessLicense;

    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人")
    private String legalRepresentative;

    /**
     * 蛋品区
     */
    @ApiModelProperty("蛋品区")
    private Boolean eggArea;
}
