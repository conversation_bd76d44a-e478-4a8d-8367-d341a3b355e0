package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/28 10:50
 */
@Setter
@Getter
@ToString
@ApiModel("客户经营户")
public class CustomerBusinessVo implements Serializable {

    private static final long serialVersionUID = -955737320461097335L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 物业编号
     */
    @ApiModelProperty("物业编号")
    private String realtySerialNo;

    /**
     * 招牌号
     */
    @ApiModelProperty("招牌号")
    private String signNo;

    /**
     * 营业执照全称
     */
    @ApiModelProperty("营业执照全称")
    private String businessLicense;

    /**
     * 法人代表
     */
    @ApiModelProperty("法人代表")
    private String legalRepresentative;

    /**
     * 主联系方式
     */
    @ApiModelProperty("主联系方式")
    private String mainContract;

    /**
     * 副联系方式
     */
    @ApiModelProperty("副联系方式")
    private String sideContract;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 蛋品区
     */
    @ApiModelProperty("蛋品区")
    private Boolean eggArea;
}
