package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2021/1/21 9:56
 */
@ApiModel("新冠防疫记录")
public class CustomerCovid19Vo implements Serializable {

    private static final long serialVersionUID = 2129145768612308062L;

    /**
     * 操作日期
     */
    @ApiModelProperty("操作日期")
    private LocalDate operateDate;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    public LocalDate getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(LocalDate operateDate) {
        this.operateDate = operateDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "CustomerCovid19Vo{" +
                "operateDate=" + operateDate +
                ", remark='" + remark + '\'' +
                '}';
    }
}
