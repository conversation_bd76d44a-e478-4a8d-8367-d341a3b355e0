package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/31 9:06
 */
@ApiModel("客户查询参数")
public class CustomerSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1872631875924251001L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("编号")
    private String serialNo;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("身份证")
    private String idcard;

    @ApiModelProperty("联系方式")
    private String telephone;

    @ApiModelProperty("工作地 - 经营区域id")
    private Long workplaceRegionId;

    @ApiModelProperty("工作地 - 街道id")
    private Long workplaceStreetId;

    @ApiModelProperty("是否做了核酸检测")
    private Boolean natTested;

    @ApiModelProperty("是否注射了新冠疫苗")
    private Boolean covid19Vaccination;

    public CustomerSearchVo() {
        super();
    }

    public CustomerSearchVo(int pageNo, int pageSize) {
        setPageNo(pageNo);
        setPageSize(pageSize);
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Long getWorkplaceRegionId() {
        return workplaceRegionId;
    }

    public void setWorkplaceRegionId(Long workplaceRegionId) {
        this.workplaceRegionId = workplaceRegionId;
    }

    public Long getWorkplaceStreetId() {
        return workplaceStreetId;
    }

    public void setWorkplaceStreetId(Long workplaceStreetId) {
        this.workplaceStreetId = workplaceStreetId;
    }

    public Boolean getNatTested() {
        return natTested;
    }

    public void setNatTested(Boolean natTested) {
        this.natTested = natTested;
    }

    public Boolean getCovid19Vaccination() {
        return covid19Vaccination;
    }

    public void setCovid19Vaccination(Boolean covid19Vaccination) {
        this.covid19Vaccination = covid19Vaccination;
    }

    @Override
    public String toString() {
        return "CustomerSearchVo{"
                + "keyword='" + keyword + '\''
                + ", name='" + name + '\''
                + ", idcard='" + idcard + '\''
                + ", telephone='" + telephone + '\''
                + ", workplaceRegionId=" + workplaceRegionId
                + ", workplaceStreetId=" + workplaceStreetId
                + ", natTested=" + natTested
                + ", covid19Vaccination=" + covid19Vaccination
                + '}';
    }
}
