package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.validator.GenderChecker;
import com.senox.user.validator.TelephoneChecker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/31 11:05
 */
@ApiModel("客户")
public class CustomerVo implements Serializable {

    private static final long serialVersionUID = 9200284018690951745L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("编号")
    private String serialNo;

    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("证件号")
    private String idcard;

    @ApiModelProperty(value = "证件类型", notes = "0身份证；2港澳台居住证")
    private Integer idcardType;

    @ApiModelProperty(value = "性别", notes = "1男；2女；3其他")
    @GenderChecker(message = "无效的性别")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("出生日期")
    private LocalDate bornDate;

    @ApiModelProperty("籍贯")
    private String nativePlace;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("手机")
    @TelephoneChecker(message = "无效的手机")
    private String telephone;

    @ApiModelProperty("邮箱")
    @Email(message = "无效的邮箱")
    private String email;

    @ApiModelProperty("省份id")
    @Min(value = 0, message = "无效的省份id")
    private Long provinceId;

    @ApiModelProperty("省份名")
    private String provinceName;

    @ApiModelProperty("城市id")
    @Min(value = 0, message = "无效的城市id")
    private Long cityId;

    @ApiModelProperty("城市名")
    private String cityName;

    @ApiModelProperty("工作地 - 区域id")
    @Min(value = 0, message = "无效的工作地区域id")
    private Long workplaceRegionId;

    @ApiModelProperty("工作地 - 区域名")
    private String workplaceRegionName;

    @ApiModelProperty("工作地 - 街道id")
    @Min(value = 0, message = "无效的工作地街道id")
    private Long workplaceStreetId;

    @ApiModelProperty("工作地 - 街道名")
    private String workplaceStreetName;

    @ApiModelProperty("工作地址")
    private String workplaceAddress;

    @ApiModelProperty("行业id")
    @Min(value = 0, message = "无效的行业id")
    private Long professionId;

    @ApiModelProperty("行业名")
    private String professionName;

    @ApiModelProperty("职位")
    private String jobTitle;

    @ApiModelProperty("核酸检测")
    private Boolean natTested;

    @ApiModelProperty("新冠疫苗")
    private Boolean covid19Vaccination;

    @ApiModelProperty("银行账号")
    private String bankAccount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("核酸检测记录")
    private List<CustomerCovid19Vo> natList;

    @ApiModelProperty("新冠疫苗记录")
    private List<CustomerCovid19Vo> vaccineList;

    @ApiModelProperty(value = "禁用", hidden = true)
    private Boolean disabled;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public Integer getIdcardType() {
        return idcardType;
    }

    public void setIdcardType(Integer idcardType) {
        this.idcardType = idcardType;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public LocalDate getBornDate() {
        return bornDate;
    }

    public void setBornDate(LocalDate bornDate) {
        this.bornDate = bornDate;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Long getWorkplaceRegionId() {
        return workplaceRegionId;
    }

    public void setWorkplaceRegionId(Long workplaceRegionId) {
        this.workplaceRegionId = workplaceRegionId;
    }

    public String getWorkplaceRegionName() {
        return workplaceRegionName;
    }

    public void setWorkplaceRegionName(String workplaceRegionName) {
        this.workplaceRegionName = workplaceRegionName;
    }

    public Long getWorkplaceStreetId() {
        return workplaceStreetId;
    }

    public void setWorkplaceStreetId(Long workplaceStreetId) {
        this.workplaceStreetId = workplaceStreetId;
    }

    public String getWorkplaceStreetName() {
        return workplaceStreetName;
    }

    public void setWorkplaceStreetName(String workplaceStreetName) {
        this.workplaceStreetName = workplaceStreetName;
    }

    public String getWorkplaceAddress() {
        return workplaceAddress;
    }

    public void setWorkplaceAddress(String workplaceAddress) {
        this.workplaceAddress = workplaceAddress;
    }

    public Long getProfessionId() {
        return professionId;
    }

    public void setProfessionId(Long professionId) {
        this.professionId = professionId;
    }

    public String getProfessionName() {
        return professionName;
    }

    public void setProfessionName(String professionName) {
        this.professionName = professionName;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Boolean getNatTested() {
        return natTested;
    }

    public void setNatTested(Boolean natTested) {
        this.natTested = natTested;
    }

    public Boolean getCovid19Vaccination() {
        return covid19Vaccination;
    }

    public void setCovid19Vaccination(Boolean covid19Vaccination) {
        this.covid19Vaccination = covid19Vaccination;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public List<CustomerCovid19Vo> getNatList() {
        return natList;
    }

    public void setNatList(List<CustomerCovid19Vo> natList) {
        this.natList = natList;
    }

    public List<CustomerCovid19Vo> getVaccineList() {
        return vaccineList;
    }

    public void setVaccineList(List<CustomerCovid19Vo> vaccineList) {
        this.vaccineList = vaccineList;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    @Override
    public String toString() {
        return "CustomerVo{"
                + "id=" + id
                + ", serialNo='" + serialNo + '\''
                + ", name='" + name + '\''
                + ", idcard='" + idcard + '\''
                + ", idcardType=" + idcardType
                + ", gender=" + gender
                + ", nation='" + nation + '\''
                + ", bornDate=" + bornDate
                + ", nativePlace='" + nativePlace + '\''
                + ", address='" + address + '\''
                + ", telephone='" + telephone + '\''
                + ", email='" + email + '\''
                + ", provinceId=" + provinceId
                + ", provinceName='" + provinceName + '\''
                + ", cityId=" + cityId
                + ", cityName='" + cityName + '\''
                + ", workplaceRegionId=" + workplaceRegionId
                + ", workplaceRegionName='" + workplaceRegionName + '\''
                + ", workplaceStreetId=" + workplaceStreetId
                + ", workplaceStreetName='" + workplaceStreetName + '\''
                + ", workplaceAddress='" + workplaceAddress + '\''
                + ", professionId=" + professionId
                + ", professionName='" + professionName + '\''
                + ", jobTitle='" + jobTitle + '\''
                + ", natTested=" + natTested
                + ", covid19Vaccination=" + covid19Vaccination
                + ", bankAccount='" + bankAccount + '\''
                + ", remark='" + remark + '\''
                + ", avatar='" + avatar + '\''
                + ", natList=" + natList
                + ", vaccineList=" + vaccineList
                + ", disabled=" + disabled
                + ", modifiedTime=" + modifiedTime
                + '}';
    }
}
