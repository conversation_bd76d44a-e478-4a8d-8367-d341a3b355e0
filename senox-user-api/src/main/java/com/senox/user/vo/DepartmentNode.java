package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/17 9:58
 */
@ApiModel("部门节点")
public class DepartmentNode implements Serializable {

    private static final long serialVersionUID = -5869571925545254262L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("部门名称")
    private String name;

    @ApiModelProperty("子节点")
    private List<DepartmentNode> childNodes;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<DepartmentNode> getChildNodes() {
        return childNodes;
    }

    public void setChildNodes(List<DepartmentNode> childNodes) {
        this.childNodes = childNodes;
    }
}
