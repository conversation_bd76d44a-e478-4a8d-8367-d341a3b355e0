package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/9/15 11:51
 */
@ApiModel("部门")
public class DepartmentVo implements Serializable {

    private static final long serialVersionUID = -4420699578061707789L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("部门名")
    @NotBlank(message = "名称不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("部门全称")
    private String fullName;

    @ApiModelProperty("排序号")
    private Integer orderNo;

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty(value = "禁用", hidden = true)
    private Boolean disabled;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }
}
