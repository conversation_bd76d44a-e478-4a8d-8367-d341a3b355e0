package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

public class DiningInformationSearchVo extends PageRequest {

    private static final long serialVersionUID = -3118220537992746674L;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("企业")
    private String company;

    @ApiModelProperty("用户")
    private String employeeName;

    @ApiModelProperty("预定时间起")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private LocalDate dateStart;
    @ApiModelProperty("预定时间止")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private LocalDate dateEnd;
    @ApiModelProperty("是否就餐")
    private Integer dining;

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }


    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public LocalDate getDateStart() {
        return dateStart;
    }

    public void setDateStart(LocalDate dateStart) {
        this.dateStart = dateStart;
    }

    public LocalDate getDateEnd() {
        return dateEnd;
    }

    public void setDateEnd(LocalDate dateEnd) {
        this.dateEnd = dateEnd;
    }

    public Integer getDining() {
        return dining;
    }

    public void setDining(Integer dining) {
        this.dining = dining;
    }
}
