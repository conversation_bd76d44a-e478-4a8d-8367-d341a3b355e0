package com.senox.user.vo;


import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class DiningInformationVo implements Serializable {
    private static final long serialVersionUID = 7984747335111822618L;

    @ApiModelProperty("编号")
    private Long id;

    @ApiModelProperty("用户")
    private String employeeName;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("部门名")
    private String departmentName;

    @ApiModelProperty("就餐日期")
    private LocalDate mealDate;

    @ApiModelProperty("就餐时间")
    private LocalTime mealTime;

    @ApiModelProperty("是否就餐")
    private Integer  dining;

    @ApiModelProperty("公司名称")
    private String companyName;


    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public LocalDate getMealDate() {
        return mealDate;
    }

    public void setMealDate(LocalDate mealDate) {
        this.mealDate = mealDate;
    }

    public LocalTime getMealTime() {
        return mealTime;
    }

    public void setMealTime(LocalTime mealTime) {
        this.mealTime = mealTime;
    }

    public Integer getDining() {
        return dining;
    }

    public void setDining(Integer dining) {
        this.dining = dining;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }
}
