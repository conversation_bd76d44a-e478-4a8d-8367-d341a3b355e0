package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 11:36
 */
@ApiModel("员工查询参数")
public class EmployeeSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = -6118681956398204548L;

    @ApiModelProperty("用户姓名")
    private String username;

    @ApiModelProperty("企业名")
    private String companyName;

    @ApiModelProperty("绑定的微信号")
    private String mpOpenid;

    @ApiModelProperty("用户姓名列表")
    private List<String> usernames;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getMpOpenid() {
        return mpOpenid;
    }

    public void setMpOpenid(String mpOpenid) {
        this.mpOpenid = mpOpenid;
    }

    public List<String> getUsernames() {
        return usernames;
    }

    public void setUsernames(List<String> usernames) {
        this.usernames = usernames;
    }
}
