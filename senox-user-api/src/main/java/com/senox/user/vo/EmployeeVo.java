package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 13:48
 */
@ApiModel("员工")
public class EmployeeVo implements Serializable {

    private static final long serialVersionUID = 7984747335111822618L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("姓名")
    @NotBlank(message = "员工姓名不能为空", groups = Add.class)
    private String username;

    @ApiModelProperty("公司名称")
    @NotBlank(message = "员工公司不能为空", groups = Add.class)
    private String companyName;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("公众号绑定的微信id")
    private String mpOpenid;

    @ApiModelProperty("默认报餐人数")
    private Integer defaultBooked;

    @ApiModelProperty("餐厅管理员")
    private Boolean canteenMaster;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("代理员工列表")
    private List<EmployeeVo> delegators;

    @ApiModelProperty("代理企业")
    private CompanyVo delegateCompany;

    @ApiModelProperty(value = "禁用", hidden = true)
    private Boolean disabled;

    @ApiModelProperty("最近修改时间")
    private LocalDateTime modifiedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getMpOpenid() {
        return mpOpenid;
    }

    public void setMpOpenid(String mpOpenid) {
        this.mpOpenid = mpOpenid;
    }

    public Integer getDefaultBooked() {
        return defaultBooked;
    }

    public void setDefaultBooked(Integer defaultBooked) {
        this.defaultBooked = defaultBooked;
    }

    public Boolean getCanteenMaster() {
        return canteenMaster;
    }

    public void setCanteenMaster(Boolean canteenMaster) {
        this.canteenMaster = canteenMaster;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<EmployeeVo> getDelegators() {
        return delegators;
    }

    public void setDelegators(List<EmployeeVo> delegators) {
        this.delegators = delegators;
    }

    public CompanyVo getDelegateCompany() {
        return delegateCompany;
    }

    public void setDelegateCompany(CompanyVo delegateCompany) {
        this.delegateCompany = delegateCompany;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }
}
