package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/14 14:38
 */
@Getter
@Setter
@ToString
@ApiModel("经营户物业关系")
public class EnterpriseRealtyVo implements Serializable {

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("物业编号别名")
    private String realtyAlias;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("多经营户共用")
    private Boolean realtyShared;
}
