package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/8/14 9:52
 */
@Getter
@Setter
@ToString
@ApiModel("企业查询")
public class EnterpriseSearchVo extends PageRequest {

    private static final long serialVersionUID = 3399985985984287842L;

    @ApiModelProperty("企业名")
    private String name;

    @ApiModelProperty("企业负责人")
    private String chargeMan;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("经营类别")
    private String category;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("物业地址")
    private String address;

    @ApiModelProperty("重点消防场所")
    private Boolean firefightingEmphasis;

    @ApiModelProperty("物业编号")
    private String realtySerial;
}
