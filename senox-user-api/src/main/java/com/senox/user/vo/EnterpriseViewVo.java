package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/13 10:07
 */
@Getter
@Setter
@ToString
@ApiModel("企业")
public class EnterpriseViewVo implements Serializable {

    private static final long serialVersionUID = 4107429200752632931L;

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的企业名")
    @ApiModelProperty("企业名")
    private String name;

    @ApiModelProperty("营业执照名称")
    private String fullName;

    @ApiModelProperty("负责人")
    private String chargeMan;

    @ApiModelProperty("联系方式1")
    private String contact1;

    @ApiModelProperty("联系方式2")
    private String contact2;

    @ApiModelProperty("经营范围")
    private String category;

    @ApiModelProperty("经营范围描述")
    private String categoryDesc;

    @ApiModelProperty("其他经营范围")
    private String otherCategory;

    @ApiModelProperty("重点消防场所")
    private Boolean firefightingEmphasis;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("物业")
    private List<EnterpriseRealtyVo> realtyList;
}
