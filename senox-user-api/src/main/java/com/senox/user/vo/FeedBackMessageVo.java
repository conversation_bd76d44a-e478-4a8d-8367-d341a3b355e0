package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/12 8:18
 */
@ApiModel("意见反馈发送消息")
public class FeedBackMessageVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("微信用户id")
    private String openid;

    @ApiModelProperty("id")
    private Long id;

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public FeedBackMessageVo() {
    }

    public FeedBackMessageVo(Long id, String openid) {
        this.openid = openid;
        this.id = id;
    }
}
