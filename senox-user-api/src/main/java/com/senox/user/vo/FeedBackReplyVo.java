package com.senox.user.vo;

import com.senox.common.annotation.DebounceParam;
import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/1 9:31
 */
@ApiModel("意见反馈回复")
public class FeedBackReplyVo implements Serializable {

    private static final long serialVersionUID = 3477830001953798203L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("微信用户id")
    private String openid;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("建议评论id")
    @NotNull(message = "建议评论id 不能为空", groups = Add.class)
    @DebounceParam
    private Long feedBackId;

    @ApiModelProperty("评论内容")
    @NotBlank(message = "评论内容不能为空", groups = Add.class)
    private String content;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("父级评论")
    private Long parentId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getFeedBackId() {
        return feedBackId;
    }

    public void setFeedBackId(Long feedBackId) {
        this.feedBackId = feedBackId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
