package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/5/31 17:25
 */
@ApiModel("建议回复查询参数")
public class FeedBackSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = 3477830001953798203L;

    @ApiModelProperty("用户名")
    private String name;

    @ApiModelProperty("微信用户id")
    private String openid;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("回复状态")
    private Boolean replyState;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Boolean getReplyState() {
        return replyState;
    }

    public void setReplyState(Boolean replyState) {
        this.replyState = replyState;
    }
}
