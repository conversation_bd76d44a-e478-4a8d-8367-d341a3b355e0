package com.senox.user.vo;

import com.senox.common.annotation.DebounceParam;
import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/1 8:20
 */
@ApiModel("意见反馈")
public class FeedBackVo implements Serializable {

    private static final long serialVersionUID = 3477830001953798203L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("微信用户id")
    @DebounceParam
    private String openid;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("是否匿名")
    private Boolean anonymous;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("反馈内容")
    @NotBlank(message = "反馈内容不能为空", groups = Add.class)
    private String content;

    @ApiModelProperty("回复状态")
    private Boolean replyState;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("媒体地址")
    private List<String> mediaUrls;

    @ApiModelProperty("建议回复列表")
    private List<FeedBackReplyVo> feedBackReplyVoList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Boolean getAnonymous() {
        return anonymous;
    }

    public void setAnonymous(Boolean anonymous) {
        this.anonymous = anonymous;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getMediaUrls() {
        return mediaUrls;
    }

    public void setMediaUrls(List<String> mediaUrls) {
        this.mediaUrls = mediaUrls;
    }

    public Boolean getReplyState() {
        return replyState;
    }

    public void setReplyState(Boolean replyState) {
        this.replyState = replyState;
    }

    public List<FeedBackReplyVo> getFeedBackReplyVoList() {
        return feedBackReplyVoList;
    }

    public void setFeedBackReplyVoList(List<FeedBackReplyVo> feedBackReplyVoList) {
        this.feedBackReplyVoList = feedBackReplyVoList;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
