package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/4/20 16:58
 */
@ApiModel("假期搜索参数")
public class HolidaySearchVo implements Serializable {

    private static final long serialVersionUID = 7284432145112524590L;

    @ApiModelProperty("开始时间")
    private LocalDate startDate;

    @ApiModelProperty("结束时间")
    private LocalDate endDate;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
}
