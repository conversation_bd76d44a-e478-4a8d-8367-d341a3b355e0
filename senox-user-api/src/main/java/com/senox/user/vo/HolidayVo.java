package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 假期
 * <AUTHOR>
 * @date 2021/4/19 10:21
 */
@ApiModel("假期")
public class HolidayVo implements Serializable {

    private static final long serialVersionUID = 9034207445882346907L;

    @ApiModelProperty("日期")
    private LocalDate holiday;

    @ApiModelProperty("描述")
    private String description;


    public LocalDate getHoliday() {
        return holiday;
    }

    public void setHoliday(LocalDate holiday) {
        this.holiday = holiday;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
