package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/3/15 16:55
 */
@ApiModel("ic卡查询参数")
public class IcCardSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = 5291119795345696247L;

    @ApiModelProperty("卡号")
    private String cardNo;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("发卡时间始")
    private LocalDateTime grantTimeStart;

    @ApiModelProperty("发卡时间止")
    private LocalDateTime grantTimeEnd;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCustomerSerial() {
        return customerSerial;
    }

    public void setCustomerSerial(String customerSerial) {
        this.customerSerial = customerSerial;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public LocalDateTime getGrantTimeStart() {
        return grantTimeStart;
    }

    public void setGrantTimeStart(LocalDateTime grantTimeStart) {
        this.grantTimeStart = grantTimeStart;
    }

    public LocalDateTime getGrantTimeEnd() {
        return grantTimeEnd;
    }

    public void setGrantTimeEnd(LocalDateTime grantTimeEnd) {
        this.grantTimeEnd = grantTimeEnd;
    }
}
