package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/3/16 9:08
 */
@ApiModel("IC卡视图对象")
public class IcCardVo implements Serializable {

    private static final long serialVersionUID = -852898072196378204L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("卡号")
    private String cardNo;

    @ApiModelProperty("客户编号")
    private String customerSerial;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("账户编号")
    private String accountNo;

    @ApiModelProperty("工本费")
    private BigDecimal foregift;

    @ApiModelProperty("余额")
    private BigDecimal balance;

    @ApiModelProperty("发放日期")
    private LocalDateTime grantTime;

    @ApiModelProperty("回收日期")
    private LocalDateTime retrieveTime;

    @ApiModelProperty("主子卡类别")
    private Boolean master;

    @ApiModelProperty("状态 0未用；1申领；2挂失；3作废")
    private Integer status;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCustomerSerial() {
        return customerSerial;
    }

    public void setCustomerSerial(String customerSerial) {
        this.customerSerial = customerSerial;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public BigDecimal getForegift() {
        return foregift;
    }

    public void setForegift(BigDecimal foregift) {
        this.foregift = foregift;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public LocalDateTime getGrantTime() {
        return grantTime;
    }

    public void setGrantTime(LocalDateTime grantTime) {
        this.grantTime = grantTime;
    }

    public LocalDateTime getRetrieveTime() {
        return retrieveTime;
    }

    public void setRetrieveTime(LocalDateTime retrieveTime) {
        this.retrieveTime = retrieveTime;
    }

    public Boolean getMaster() {
        return master;
    }

    public void setMaster(Boolean master) {
        this.master = master;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
