package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023-11-1
 */
@Setter
@Getter
@ApiModel("商户申请")
public class MerchantAuthApplyEditVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @NotBlank(message = "无效的商户姓名")
    @ApiModelProperty("商户姓名")
    private String merchantName;

    @NotBlank(message = "无效的身份证")
    @ApiModelProperty("身份证")
    private String idcard;

    @NotBlank(message = "无效的联系方式")
    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("冷藏客户编号")
    private String rcSerial;

    @ApiModelProperty("三轮车配送权限")
    private Boolean bicycleAuth;

    @ApiModelProperty("推荐码")
    private String referralCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("验证码")
    private String verifyCode;

    @ApiModelProperty(value = "创建人", hidden = true)
    private String createOpenid;
}
