package com.senox.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/7 17:06
 */
@Getter
@Setter
@ToString
public class MerchantAuthApplyListVo implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("商户名称")
    private String merchantName;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("身份证")
    private String idcard;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("冷藏编号")
    private String rcSerial;

    @ApiModelProperty("三轮车权限")
    private Boolean bicycleAuth;

    @ApiModelProperty("干仓权限")
    private Boolean dryAuth;

    @ApiModelProperty("推荐码")
    private String referralCode;

    @ApiModelProperty("申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审批结果")
    private Integer auditStatus;

    @ApiModelProperty("审批人")
    private String auditMan;

    @ApiModelProperty("审批时间")
    private LocalDateTime auditTime;
}
