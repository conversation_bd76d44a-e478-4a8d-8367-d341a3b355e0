package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-1
 */
@Getter
@Setter
@ToString
@ApiModel("商户权限申请查询")
public class MerchantAuthApplySearchVo extends PageRequest {

    private static final long serialVersionUID = 6542206211034434691L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("商户名")
    private String merchantName;

    @ApiModelProperty("冷藏编号")
    private String rcSerial;

    @ApiModelProperty("冷藏客户")
    private Boolean rc;

    @ApiModelProperty("三轮车权限")
    private Boolean bicycleAuth;

    @ApiModelProperty("干仓权限")
    private Boolean dryAuth;

    @ApiModelProperty("审批状态")
    private List<Integer> statusList;

    @ApiModelProperty("创建人")
    private String createOpenid;

    @ApiModelProperty("申请时间起")
    private LocalDateTime applyTimeStart;

    @ApiModelProperty("申请时间止")
    private LocalDateTime applyTimeEnd;

    @ApiModelProperty("审批时间起")
    private LocalDateTime auditTimeStart;

    @ApiModelProperty("审批时间止")
    private LocalDateTime auditTimeEnd;

    @ApiModelProperty("脱敏")
    private Boolean desensitized = true;

}
