package com.senox.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/7 17:27
 */
@Getter
@Setter
@ToString
public class MerchantAuthApplyVo implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @NotBlank(message = "无效的商户姓名")
    @ApiModelProperty("商户姓名")
    private String merchantName;

    @NotBlank(message = "无效的身份证")
    @ApiModelProperty("身份证")
    private String idcard;

    @NotBlank(message = "无效的联系方式")
    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("冷藏客户编号")
    private String rcSerial;

    @ApiModelProperty("三轮车配送权限")
    private Boolean bicycleAuth;

    @ApiModelProperty("干仓权限")
    private Boolean dryAuth;

    @ApiModelProperty("推荐码")
    private String referralCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty("审批结果")
    private Integer auditStatus;

    @ApiModelProperty("审批人")
    private String auditMan;

    @ApiModelProperty("审批备注")
    private String auditRemark;

    @ApiModelProperty("审批时间")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "申请人id", hidden = true)
    private String createOpenid;
}
