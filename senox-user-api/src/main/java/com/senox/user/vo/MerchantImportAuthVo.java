package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/6 14:10
 */
@ApiModel("商户导入权限类别")
@Data
public class MerchantImportAuthVo implements Serializable {

    private static final long serialVersionUID = -4450770003227938056L;

    @ApiModelProperty("三轮车权限")
    private Boolean bicycleAuth;

    @ApiModelProperty("干仓权限")
    private Boolean dryAuth;

    @ApiModelProperty("多多权限")
    private Boolean duoduo;

    @ApiModelProperty("冷藏权限")
    private Boolean rc;
}
