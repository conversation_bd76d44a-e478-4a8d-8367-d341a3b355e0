package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-30
 */
@Getter
@Setter
@ApiModel("商户查询")
public class MerchantSearchVo extends PageRequest {

    private static final long serialVersionUID = 6542206211034434691L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("商户名")
    private String name;

    @ApiModelProperty("冷藏客户编号")
    private String rcSerial;

    @ApiModelProperty("冷藏客户")
    private Boolean rc;

    @ApiModelProperty("多多客户")
    private Boolean duoduo;

    @ApiModelProperty("三轮车权限")
    private Boolean bicycleAuth;

    @ApiModelProperty("干仓权限")
    private Boolean dryAuth;

    @ApiModelProperty("脱敏")
    private Boolean desensitized = true;

    @ApiModelProperty("id列表")
    private List<Long> ids;

    @ApiModelProperty("结算周期")
    private Long settlePeriod;

    @ApiModelProperty("收费标准id")
    private Long chargesId;

}
