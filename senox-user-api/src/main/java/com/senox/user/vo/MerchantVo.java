package com.senox.user.vo;

import com.senox.user.constant.MerchantBillSettlePeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Setter
@Getter
@ToString
@ApiModel("商户")
public class MerchantVo {

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "无效的商户名")
    @ApiModelProperty("商户名")
    private String name;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("身份证")
    private String idcard;

    @ApiModelProperty("通讯地址")
    private String address;

    @ApiModelProperty("冷藏客户编号")
    private String rcSerial;

    @ApiModelProperty("冷藏抬头")
    private String rcTaxHeader;

    @ApiModelProperty("多多客户")
    private Boolean duoduo;

    @ApiModelProperty("结算周期")
    private MerchantBillSettlePeriod settlePeriod;

    @ApiModelProperty("三轮车权限")
    private Boolean bicycleAuth;

    @ApiModelProperty("干仓权限")
    private Boolean dryAuth;

    @ApiModelProperty("推荐码")
    private String referralCode;

    @ApiModelProperty("三轮车收费标准id")
    private Long bicycleChargesId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("主账号")
    private Boolean master;

    @ApiModelProperty("是否绑定")
    private Boolean bind;
}
