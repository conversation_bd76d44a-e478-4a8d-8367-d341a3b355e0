package com.senox.user.vo;

import com.senox.user.constant.PrizeDrawNumberType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/15 14:14
 */
@ApiModel("抽奖次数记录")
@Data
public class PrizeDrawNumberVo implements Serializable {
    private static final long serialVersionUID = 7810308974974343498L;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activityId;

    /**
     * openid
     */
    @ApiModelProperty("openid")
    private String openid;

    /**
     * 获取票数方式
     * @see PrizeDrawNumberType
     */
    @ApiModelProperty("获取票数方式(0:关注;1:转发;)")
    private Integer type;
}
