package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/11 10:59
 */
@ApiModel("抽奖记录查询参数")
@Getter
@Setter
public class PrizeRecordsSearchVo extends PageRequest {
    private static final long serialVersionUID = -7788269299650281863L;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activityId;

    /**
     * 奖品id，NULL表示未中奖
     */
    @ApiModelProperty("奖品id，NULL表示未中奖")
    private Long prizeId;

    /**
     * openid
     */
    @ApiModelProperty("openid")
    private String openid;

    /**
     * 是否中奖
     */
    @ApiModelProperty("是否中奖")
    private Boolean isWin;

    /**
     * 是否核销
     */
    @ApiModelProperty("是否核销")
    private Boolean isVerify;

    /**
     * 创建时间起
     */
    @ApiModelProperty("创建时间起")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间止
     */
    @ApiModelProperty("创建时间止")
    private LocalDateTime createTimeEnd;
}
