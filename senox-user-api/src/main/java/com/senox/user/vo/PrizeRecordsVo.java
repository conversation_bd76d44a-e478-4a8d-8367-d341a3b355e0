package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/11 10:56
 */
@ApiModel("抽奖记录")
@Data
public class PrizeRecordsVo implements Serializable {

    private static final long serialVersionUID = 7664389117949722731L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * uuid
     */
    @ApiModelProperty("uuid")
    private String uuid;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activityId;

    /**
     * 活动名
     */
    @ApiModelProperty("活动名")
    private String activityName;

    /**
     * 奖品id，NULL表示未中奖
     */
    @ApiModelProperty("奖品id，NULL表示未中奖")
    private Long prizeId;

    /**
     * 奖品名
     */
    @ApiModelProperty("奖品名")
    private String prizeName;

    /**
     * 奖品描述
     */
    @ApiModelProperty("奖品描述")
    private String prizeDescription;

    /**
     * 奖品媒体资源
     */
    @ApiModelProperty("奖品媒体资源")
    private String prizeMediaUrl;

    /**
     * openid
     */
    @ApiModelProperty("openid")
    private String openid;

    /**
     * 是否中奖
     */
    @ApiModelProperty("是否中奖")
    private Boolean isWin;

    /**
     * 是否核销
     */
    @ApiModelProperty("是否核销")
    private Boolean isVerify;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
