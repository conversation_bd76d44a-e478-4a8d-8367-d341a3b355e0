package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2025/4/10 13:56
 */
@ApiModel("奖品查询参数")
@Getter
@Setter
public class PrizeSearchVo extends PageRequest {
    private static final long serialVersionUID = 7211427641408051714L;

    /**
     * 抽奖活动id
     */
    @ApiModelProperty("抽奖活动id")
    private Long activityId;

    /**
     * 奖品名称
     */
    @ApiModelProperty("奖品名称")
    private String name;

    /**
     * 剩余数量检查
     */
    @ApiModelProperty("剩余数量检查")
    private Boolean remainingNumCheck;
}
