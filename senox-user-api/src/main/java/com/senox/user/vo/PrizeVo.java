package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/10 13:50
 */
@ApiModel("奖品")
@Data
public class PrizeVo implements Serializable {
    private static final long serialVersionUID = -3899152015896152253L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 抽奖活动id
     */
    @ApiModelProperty("抽奖活动id")
    private Long activityId;

    /**
     * 奖品名称
     */
    @ApiModelProperty("奖品名称")
    private String name;

    /**
     * 奖品描述
     */
    @ApiModelProperty("奖品描述")
    private String description;

    /**
     * 媒体资源
     */
    @ApiModelProperty("媒体资源")
    private String mediaUrl;

    /**
     * 奖品总数量
     */
    @ApiModelProperty("奖品总数量")
    private Integer totalNum;

    /**
     * 剩余奖品数量
     */
    @ApiModelProperty("剩余奖品数量")
    private Integer remainingNum;

    /**
     * 中奖概率(0-100)
     */
    @ApiModelProperty("中奖概率(0-100)")
    private Integer probability;
}
