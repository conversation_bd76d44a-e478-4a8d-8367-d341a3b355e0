package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:05
 */
@Getter
@Setter
@ApiModel("预约记录查询参数")
public class ReservationRecordSearchVo extends PageRequest {

    /**
     * 访客姓名
     */
    @ApiModelProperty("访客姓名")
    private String visitorName;

    /**
     * 拜访时间起
     */
    @ApiModelProperty("拜访时间起")
    private LocalDateTime visitTimeStart;

    /**
     * 拜访时间止
     */
    @ApiModelProperty("拜访时间止")
    private LocalDateTime visitTimeEnd;

    /**
     * 申请人openid
     */
    @ApiModelProperty("申请人openid")
    private String createOpenid;

    /**
     * 预约类型
     * @see com.senox.user.constant.ReservationType
     */
    @ApiModelProperty("预约类型 0年货节 1开仓节")
    private Integer type;

    /**
     * 车牌
     */
    @ApiModelProperty("车牌")
    private String carNo;
}
