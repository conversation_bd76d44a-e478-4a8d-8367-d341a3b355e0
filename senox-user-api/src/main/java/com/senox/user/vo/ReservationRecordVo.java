package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:02
 */
@Data
@ApiModel("预约记录")
public class ReservationRecordVo implements Serializable {

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups =  Update.class)
    private Long id;

    /**
     * 访客姓名
     */
    @ApiModelProperty("访客姓名")
    private String visitorName;

    /**
     * 访客电话
     */
    @ApiModelProperty("访客电话")
    private String contact;

    /**
     * 随行人数
     */
    @ApiModelProperty("随行人数")
    private Integer togetherNum;

    /**
     * 拜访时间起
     */
    @ApiModelProperty("拜访时间起")
    @NotNull(message = "无效的拜访时间起", groups = {Add.class, Update.class})
    private LocalDateTime visitTimeStart;

    /**
     * 拜访时间止
     */
    @ApiModelProperty("拜访时间止")
    @NotNull(message = "无效的拜访时间止", groups = {Add.class, Update.class})
    private LocalDateTime visitTimeEnd;

    /**
     * 申请人openid
     */
    @ApiModelProperty("申请人openid")
    @NotBlank(message = "无效的openid", groups = {Add.class, Update.class})
    private String createOpenid;

    /**
     * 预约类型
     * @see com.senox.user.constant.ReservationType
     */
    @ApiModelProperty("预约类型 0年货节 1开仓节")
    private Integer type;

    /**
     * 车牌集合
     */
    @ApiModelProperty("车牌集合")
    private List<String> carNoList;
}
