package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/22 16:56
 */
@ApiModel("住户拥有的权限详情")
public class ResidentAccessResultVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("住户id")
    private Long id;

    @ApiModelProperty("唯一编号")
    private String residentNo;

    @ApiModelProperty("住户类型 0 住户 1 员工 2 其他")
    private Integer residentType;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("身份证号码")
    private String idNum;

    @ApiModelProperty("出生日期")
    private String bornDate;

    @ApiModelProperty("性别 1 男 2 女")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nature;

    @ApiModelProperty("电话号码")
    private String telephone;

    @ApiModelProperty("住址")
    private String address;

    @ApiModelProperty("人脸")
    private String faceUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("住户权限")
    private List<ResidentAccessVo> residentAccessVoList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getResidentNo() {
        return residentNo;
    }

    public void setResidentNo(String residentNo) {
        this.residentNo = residentNo;
    }

    public Integer getResidentType() {
        return residentType;
    }

    public void setResidentType(Integer residentType) {
        this.residentType = residentType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdNum() {
        return idNum;
    }

    public void setIdNum(String idNum) {
        this.idNum = idNum;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getNature() {
        return nature;
    }

    public void setNature(String nature) {
        this.nature = nature;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getFaceUrl() {
        return faceUrl;
    }

    public void setFaceUrl(String faceUrl) {
        this.faceUrl = faceUrl;
    }

    public List<ResidentAccessVo> getResidentAccessVoList() {
        return residentAccessVoList;
    }

    public void setResidentAccessVoList(List<ResidentAccessVo> residentAccessVoList) {
        this.residentAccessVoList = residentAccessVoList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
