package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/22 15:13
 */
@ApiModel("住户权限")
@Data
public class ResidentAccessVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("权限id")
    private Long id;

    @ApiModelProperty("唯一编号")
    @NotBlank(message = "唯一编号不能为空", groups = Add.class)
    private String residentNo;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("拥有权限")
    private Boolean access;

    @ApiModelProperty("是否生效（0：未生效，1：已生效）")
    private Boolean state;

    @ApiModelProperty("是否禁用")
    private Boolean disabled;

    @ApiModelProperty("设备名")
    private String deviceName;

    @ApiModelProperty("物业名")
    private String realtyName;

    @ApiModelProperty("区域id")
    private Long regionId;

    @ApiModelProperty("区域名")
    private String regionName;

    @ApiModelProperty("街道id")
    private Long streetId;

    @ApiModelProperty("街道名")
    private String streetName;

    @ApiModelProperty("设备id集合")
    private List<Long> deviceList;

    @ApiModelProperty("住户拥有的设备权限")
    private List<ResidentDeviceAccessVo> deviceAccessVos;

    @ApiModelProperty(value = "住户名", hidden = true)
    private String residentName;

    @ApiModelProperty("人脸照")
    private String faceUrl;

    @ApiModelProperty("设备ip")
    private String deviceIp;
}
