package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/25 14:21
 */
@ApiModel("住户验证")
public class ResidentCheckVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("姓名")
    @NotBlank(message = "姓名不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("身份证号码")
    @NotBlank(message = "身份证号码不能为空", groups = Add.class)
    private String idNum;

    @ApiModelProperty("电话号码")
    @NotBlank(message = "电话号码不能为空", groups = Add.class)
    private String telephone;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdNum() {
        return idNum;
    }

    public void setIdNum(String idNum) {
        this.idNum = idNum;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
}
