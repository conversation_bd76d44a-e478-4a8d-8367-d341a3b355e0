package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/31 8:09
 */
@ApiModel("住户拥有的设备权限")
public class ResidentDeviceAccessVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("权限id")
    private Long id;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("设备名")
    private String deviceName;

    @ApiModelProperty("是否生效（0：未生效，1：已生效）")
    private Boolean state;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Boolean getState() {
        return state;
    }

    public void setState(Boolean state) {
        this.state = state;
    }

    public ResidentDeviceAccessVo() {
    }

    public ResidentDeviceAccessVo(Long id, Long deviceId, String deviceName, Boolean state) {
        this.id = id;
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.state = state;
    }
}
