package com.senox.user.vo;

import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/22 14:39
 */
@ApiModel("人脸修改参数")
public class ResidentFaceUrlVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("人脸")
    @NotBlank(message = "人脸不能为空", groups = Update.class)
    private String faceUrl;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFaceUrl() {
        return faceUrl;
    }

    public void setFaceUrl(String faceUrl) {
        this.faceUrl = faceUrl;
    }
}
