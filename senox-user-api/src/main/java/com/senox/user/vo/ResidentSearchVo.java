package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/18 9:13
 */
@Getter
@Setter
@ToString
@ApiModel("住户查询参数")
public class ResidentSearchVo extends PageRequest implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("唯一编号")
    private String residentNo;

    @ApiModelProperty("住户类型 0 住户 1 员工 2 其他")
    private Integer residentType;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("身份证号码")
    private String idNum;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("物业编号")
    private String realtySerial;

}
