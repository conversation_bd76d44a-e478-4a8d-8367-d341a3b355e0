package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/5/18 8:13
 */
@Getter
@Setter
@ToString
@ApiModel("住户")
public class ResidentVo implements Serializable {

    private static final long serialVersionUID = -2415436765518587511L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("唯一编号")
    private String residentNo;

    @ApiModelProperty("住户类型 0 住户 1 员工 2 其他")
    @NotNull(message = "id 不能为空", groups = {Add.class,Update.class})
    private Integer residentType;

    @ApiModelProperty("姓名")
    @NotBlank(message = "姓名不能为空", groups = {Add.class, Update.class})
    private String name;

    @ApiModelProperty("身份证号码")
    @NotBlank(message = "身份证号码不能为空", groups = Add.class)
    private String idNum;

    @ApiModelProperty("出生日期")
    private String bornDate;

    @ApiModelProperty("性别 1 男 2 女")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nature;

    @ApiModelProperty("电话号码")
    @NotBlank(message = "电话号码不能为空", groups = {Add.class, Update.class})
    private String telephone;

    @ApiModelProperty("住址")
    @NotBlank(message = "住址不能为空", groups = {Add.class, Update.class})
    private String address;

    @ApiModelProperty("人脸")
    private String faceUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
