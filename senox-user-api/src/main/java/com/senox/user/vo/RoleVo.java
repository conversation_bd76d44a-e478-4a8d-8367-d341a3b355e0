package com.senox.user.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/28 16:45
 */
@ApiModel("角色")
@Data
public class RoleVo implements Serializable {

    private static final long serialVersionUID = -7166338611658715505L;

    @ApiModelProperty("id")
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 0, message = "无效id")
    private Long id;

    @ApiModelProperty("角色名")
    @NotBlank(message = "角色名不能为空", groups = Add.class)
    private String name;

    @ApiModelProperty("角色编码")
    private String code;

    @ApiModelProperty("权限列表")
    private List<Long> cosList;
}
