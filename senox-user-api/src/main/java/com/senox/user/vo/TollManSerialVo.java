package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/7/16 11:38
 */
@ApiModel("收据编号")
public class TollManSerialVo implements Serializable {

    private static final long serialVersionUID = -3293957109156468635L;

    @ApiModelProperty("收据编号")
    private String billSerial;

    @ApiModelProperty("支付终端序列号")
    private String payDeviceSn;

    public TollManSerialVo() {
    }

    public TollManSerialVo(String billSerial) {
        this.billSerial = billSerial;
    }

    public String getBillSerial() {
        return billSerial;
    }

    public void setBillSerial(String billSerial) {
        this.billSerial = billSerial;
    }

    public String getPayDeviceSn() {
        return payDeviceSn;
    }

    public void setPayDeviceSn(String payDeviceSn) {
        this.payDeviceSn = payDeviceSn;
    }
}
