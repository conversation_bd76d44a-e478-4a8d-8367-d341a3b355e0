package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2024/10/16 10:48
 */
@ApiModel("投票类别查询参数")
@Getter
@Setter
public class VoteCategorySearchVo extends PageRequest {

    private static final long serialVersionUID = -2670829831344897707L;


    @ApiModelProperty("类别名")
    private String name;

    @ApiModelProperty("活动Id")
    private Long activityId;
}
