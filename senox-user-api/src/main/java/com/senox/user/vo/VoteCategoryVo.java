package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/16 10:53
 */
@ApiModel("投票类别")
@Data
public class VoteCategoryVo implements Serializable {

    private static final long serialVersionUID = 1750662951565120180L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("类别名")
    private String name;

    @ApiModelProperty("活动Id")
    private Long activityId;
}
