package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/17 8:55
 */
@ApiModel("投票记录")
@Data
public class VoteRecordsVo implements Serializable {
    private static final long serialVersionUID = 4177302399965684294L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activityId;

    /**
     * 资源id
     */
    @ApiModelProperty("资源id")
    private Long resourcesId;

    /**
     * openid
     */
    @ApiModelProperty("openid")
    private String openid;

    /**
     * 票数
     */
    @ApiModelProperty("票数")
    private Integer numbers;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
