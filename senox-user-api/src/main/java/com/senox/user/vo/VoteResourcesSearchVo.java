package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2024/10/16 14:55
 */
@ApiModel("投票资源查询参数")
@Getter
@Setter
public class VoteResourcesSearchVo extends PageRequest {
    private static final long serialVersionUID = -3839437974062555622L;

    /**
     * 投票活动id
     */
    @ApiModelProperty("投票活动id")
    private Long activityId;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keywords;

    /**
     * 类别
     */
    @ApiModelProperty("类别")
    private Integer category;
}
