package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/16 15:00
 */
@ApiModel("投票资源")
@Data
public class VoteResourcesVo implements Serializable {
    private static final long serialVersionUID = -3104123566927690619L;

    private Long id;

    /**
     * 投票活动id
     */
    @ApiModelProperty("投票活动id")
    private Long activityId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private Integer serial;

    /**
     * 类别
     */
    @ApiModelProperty("类别")
    private Integer category;

    /**
     * 简介
     */
    @ApiModelProperty("简介")
    private String description;

    /**
     * 票数
     */
    @ApiModelProperty("票数")
    private Integer numbers;

    /**
     * 缩略图
     */
    @ApiModelProperty("缩略图")
    private String thumbnail;

    /**
     * 原图
     */
    @ApiModelProperty("原图")
    private String original;
}
