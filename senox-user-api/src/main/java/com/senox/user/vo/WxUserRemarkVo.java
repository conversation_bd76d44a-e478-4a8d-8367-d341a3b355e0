package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/14 8:39
 */
@ApiModel("微信用户备注")
@Data
public class WxUserRemarkVo implements Serializable {

    private static final long serialVersionUID = 4824839610382363577L;

    @ApiModelProperty("微信id")
    private Long id;

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("联系方式")
    private String contact;
}
