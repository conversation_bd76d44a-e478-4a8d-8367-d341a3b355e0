package com.senox.user.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/3 16:10
 */
@ApiModel("微信用户查询参数")
@Getter
@Setter
public class WxUserSearchVo extends PageRequest {

    private static final long serialVersionUID = -3118220537992746674L;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("物业编号")
    private String realtySerial;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("是否包含备注")
    private Boolean containRemark;
}
