package com.senox.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/6/3 15:24
 */
@ApiModel("微信用户")
@Getter
@Setter
@ToString
public class WxUserVo implements Serializable {

    private static final long serialVersionUID = -8218456774379043699L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("微信用户id")
    private String openid;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("性别，0未知，1男，2女")
    private Integer sex;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("国家")
    private String country;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("appId")
    private String appId;

    @ApiModelProperty("灰度用户")
    private Boolean gray;

    @ApiModelProperty("绑定的物业数")
    private Integer bindRealtyCount;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("联系方式")
    private String contact;
}
