package com.senox.user.config;

import com.senox.common.exception.UnAuthorizedException;
import com.senox.common.utils.JsonUtils;
import com.senox.context.AdminContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/12 11:53
 */
@Slf4j
public class AccessInterceptor implements HandlerInterceptor {

    /**
     * 无需拦截的url
     */
    private List<String> excludeUrls;

    public AccessInterceptor(List<String> excludeUrls) {
        this.excludeUrls = excludeUrls;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (isUrlExcluded(request.getRequestURI()) || AdminContext.isUserValid()) {
            return true;
        }

        throw new UnAuthorizedException();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) throws Exception {
        AdminContext.clear();
    }

    private boolean isUrlExcluded(String url) {
        for (String excludeUrl : excludeUrls) {
            if (url.contains(excludeUrl)) {
                return true;
            }
        }
        return false;
    }
}
