package com.senox.user.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/1/7 11:40
 */
@Component
@RefreshScope
@Configuration
public class AppConfig {

    /**
     * 密钥
     */
    @Value("${senox.user.key}")
    private String tokenKey;
    /**
     * 向量
     */
    @Value("${senox.user.iv}")
    private String tokenIv;
    /**
     * token有效时长（秒）
     */
    @Value("${senox.user.tokenTTL:7200}")
    private Long tokenTTL;
    /**
     * 盐长度
     */
    @Value("${senox.salt.length:4}")
    private Integer saltLength;
    /**
     * 加密迭代次数
     */
    @Value("${senox.hash.iterations:2}")
    private Integer hashIterations;

    public String getTokenKey() {
        return tokenKey;
    }

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public String getTokenIv() {
        return tokenIv;
    }

    public void setTokenIv(String tokenIv) {
        this.tokenIv = tokenIv;
    }

    public Long getTokenTTL() {
        return tokenTTL;
    }

    public void setTokenTTL(Long tokenTTL) {
        this.tokenTTL = tokenTTL;
    }

    public Integer getSaltLength() {
        return saltLength;
    }

    public void setSaltLength(Integer saltLength) {
        this.saltLength = saltLength;
    }

    public Integer getHashIterations() {
        return hashIterations;
    }

    public void setHashIterations(Integer hashIterations) {
        this.hashIterations = hashIterations;
    }
}
