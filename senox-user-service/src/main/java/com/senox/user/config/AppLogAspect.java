package com.senox.user.config;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RequestUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2020/12/30 8:26
 */
@Aspect
@Component
public class AppLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(AppLogAspect.class);

    private static final ThreadLocal<Long> execStartTime = new ThreadLocal<>();

    /**
     * 以 controller 包下定义的所有请求为切入点
     */

    @Pointcut("execution(public * com.senox.user.controller..*.*(..))")
    public void logPoint(){}

    @Before("logPoint()")
    public void doBefore(JoinPoint joinPoint) {
        execStartTime.set(System.currentTimeMillis());
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            Object[] arguments = {};
            if (joinPoint.getArgs() != null) {
                arguments = Stream.of(joinPoint.getArgs()).filter(x -> !isServletRequestOrResponseArgument(x)).toArray();
            }
            logger.info("Get Request ip:{}, URL:{}, arguments:{}", RequestUtils.getIpAddr(request), request.getRequestURI(),
                    JsonUtils.object2Json(arguments));
        } catch (Exception e) {
            logger.error("打印请求日志出错", e);
        }
    }

    @After("logPoint()")
    public void doAfterReturning() {
        logger.info("Request cost time: {}", System.currentTimeMillis() - execStartTime.get());
        execStartTime.remove();
    }

    private boolean isServletRequestOrResponseArgument(Object o) {
        return o instanceof ServletRequest || o instanceof ServletResponse;
    }

}
