package com.senox.user.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.senox.user.utils.RabbitMqUtils;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/5/30 10:09
 */
@Configuration
public class RabbitMqConfigure {

    @Bean
    @ConfigurationProperties(prefix = "spring.rabbitmq")
    public RabbitMqEntity rabbitMqEntity() {
        return new RabbitMqEntity();
    }

    @Bean
    public ConnectionFactory connectionFactory(RabbitMqEntity rabbitMqEntity) {
        return RabbitMqUtils.buildMQConnectionFactory(rabbitMqEntity, rabbitMqEntity.getVirtualHost());
    }

    @Bean
    public Jackson2JsonMessageConverter jackson2JsonMessageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public SimpleRabbitListenerContainerFactory containerFactory(SimpleRabbitListenerContainerFactoryConfigurer configurer,
                                                                 MessageConverter messageConverter,
                                                                 ConnectionFactory connectionFactory,
                                                                 RabbitMqEntity rabbitMqEntity) {
        return RabbitMqUtils.buildListenerContainerFactory(configurer, messageConverter, connectionFactory, rabbitMqEntity.getConsumerThreads());
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, ObjectMapper objectMapper) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(jackson2JsonMessageConverter(objectMapper));
        return rabbitTemplate;
    }

}
