package com.senox.user.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/12 11:59
 */
@Configuration
public class UserWebConfigure implements WebMvcConfigurer {

    @Value("#{'${senox.adminFilter.excludeUrls:}'.split(',')}")
    private List<String> excludeUrls;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AccessInterceptor(excludeUrls))
                .addPathPatterns("/**");
    }
}
