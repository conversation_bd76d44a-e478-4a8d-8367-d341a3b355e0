package com.senox.user.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/16 8:26
 */
@Getter
@Setter
@ConfigurationProperties("senox.user.activity")
@Configuration
public class VoteActivityConfig {

    /**
     * url
     */
    private Map<String, String> url;

}
