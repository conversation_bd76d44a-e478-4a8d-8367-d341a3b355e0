package com.senox.user.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @date 2021/4/7 13:46
 */
@Profile("!test")
@Configuration
public class XxlJobConfigure {

    private static final Logger logger = LoggerFactory.getLogger(XxlJobConfigure.class);

    /**
     * xxl-job管理后台地址
     */
    @Value("${xxl.job.admin.address}")
    private String adminAddress;
    /**
     * xxl-job accessToken
     */
    @Value("${xxl.job.accessToken}")
    private String accessToken;
    /**
     * xxl-job executor appName
     */
    @Value("${xxl.job.executor.appName}")
    private String appName;
    /**
     * xxl-job executor server info ip
     */
    @Value("${xxl.job.executor.ip}")
    private String ip;
    /**
     * xxl-job executor server info port
     */
    @Value("${xxl.job.executor.port}")
    private Integer port;
    /**
     * xxl-job executor log path
     */
    @Value("${xxl.job.executor.logPath}")
    private String logPath;
    /**
     * xxl-job executor log retention days
     */
    @Value("${xxl.job.executor.logRetentionDays}")
    private Integer logRetentionDays;


    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        logger.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobExecutor = new XxlJobSpringExecutor();
        xxlJobExecutor.setAdminAddresses(adminAddress);
        xxlJobExecutor.setAccessToken(accessToken);
        xxlJobExecutor.setAppname(appName);
        xxlJobExecutor.setIp(ip);
        xxlJobExecutor.setPort(port);
        xxlJobExecutor.setLogPath(logPath);
        xxlJobExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobExecutor;
    }
}
