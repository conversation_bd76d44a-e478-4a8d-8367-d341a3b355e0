package com.senox.user.convert;


import com.senox.common.convert.BaseConvert;
import com.senox.user.domain.AdminUser;
import com.senox.user.vo.AdminUserVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-8-29
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdminUserConvert extends BaseConvert<AdminUser, AdminUserVo> {

}
