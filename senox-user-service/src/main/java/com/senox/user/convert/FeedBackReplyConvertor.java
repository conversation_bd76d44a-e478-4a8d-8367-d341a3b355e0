package com.senox.user.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.user.domain.FeedBackReply;
import com.senox.user.vo.FeedBackReplyVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/6/1 9:39
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FeedBackReplyConvertor extends BaseConvert<FeedBackReply, FeedBackReplyVo> {

}
