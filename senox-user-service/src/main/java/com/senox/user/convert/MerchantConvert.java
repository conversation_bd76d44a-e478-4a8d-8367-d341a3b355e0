package com.senox.user.convert;

import com.senox.user.constant.MerchantBillSettlePeriod;
import com.senox.user.domain.Merchant;
import com.senox.user.vo.MerchantVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MerchantConvert {

    /**
     * vo to domain
     *
     * @param vo vo
     * @return do domain
     */
    @Mapping(target = "settlePeriod", expression = "java(getSettlePeriod(vo.getSettlePeriod()))")
    Merchant todo(MerchantVo vo);

    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    @Mapping(target = "settlePeriod", expression = "java(com.senox.user.constant.MerchantBillSettlePeriod.fromNumber(domain.getSettlePeriod()))")
    MerchantVo toVo(Merchant domain);

    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    List<MerchantVo> toVo(List<Merchant> domain);

    /**
     * vo to do
     *
     * @param vo do
     * @return vo
     */
    List<Merchant> todo(List<MerchantVo> vo);

    default Integer getSettlePeriod(MerchantBillSettlePeriod settlePeriod) {
        return null == settlePeriod ? null : settlePeriod.getNumber();
    }
}
