package com.senox.user.convert;

import com.senox.user.domain.Prize;
import com.senox.user.vo.PrizeVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/11 10:27
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PrizeConvert {

    Prize toDo(PrizeVo vo);

    List<Prize> toDo(List<PrizeVo> voList);

    PrizeVo toVo(Prize domain);

    List<PrizeVo> toVo(List<Prize> domainList);
}
