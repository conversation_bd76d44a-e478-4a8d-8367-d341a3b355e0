package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/15 16:45
 */
@TableName("u_activity")
@Getter
@Setter
public class Activity extends TableIdEntity {

    /**
     * 活动名称
     */
    private String name;

    /**
     * UUID
     */
    private String uuid;

    /**
     * 活动链接
     */
    private String url;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 背景图片
     */
    private String bgUrl;

    /**
     * 背景颜色
     */
    private String bgColor;

    /**
     * 字体颜色
     */
    private String fontColor;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享简介
     */
    private String shareBlurb;

    /**
     * 分享图片
     */
    private String shareUrl;

    /**
     * 活动简介
     */
    private String blurb;

    /**
     * 中止时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime stopTime;

    /**
     * 限制数量
     */
    private Integer limitNum;

    /**
     * @see com.senox.user.constant.ActivityStatus
     * 状态
     */
    private Integer status;

    /**
     * @see com.senox.user.constant.ActivityCategory
     * 类别
     */
    private Integer category;
}
