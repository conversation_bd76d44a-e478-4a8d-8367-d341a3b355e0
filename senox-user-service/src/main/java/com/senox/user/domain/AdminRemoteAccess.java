package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/26 8:33
 */
@Getter
@Setter
@ToString
@TableName("u_admin_remote_access")
public class AdminRemoteAccess extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 管理员id
     */
    private Long adminUserId;

    /**
     * 设备id
     */
    private Long deviceId;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        AdminRemoteAccess that = (AdminRemoteAccess) o;
        return Objects.equals(adminUserId, that.adminUserId)
                && Objects.equals(deviceId, that.deviceId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getAdminUserId(), getDeviceId());
    }
}
