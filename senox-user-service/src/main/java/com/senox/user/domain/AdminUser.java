package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 管理用户
 * <AUTHOR>
 * @Date 2020/12/28 11:21
 */
@Getter
@Setter
public class AdminUser extends BaseEntity {

    /**
     * 用户账号
     */
    private String username;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 密码
     */
    private String password;
    /**
     * 盐
     */
    private String salt;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 联系方式
     */
    private String telephone;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 部门id
     */
    private Long departmentId;
    /**
     * 是否收费员
     */
    private Boolean tollMan;
    /**
     * 0：员工，1：维修员，2：维修主管
     */
    private Integer maintainManType;
    /**
     * 登录跳转路径
     */
    private String loginPath;
    /**
     * 系统使用权
     */
    private Boolean loginable;
    /**
     * 收据编号
     */
    private String billSerial;
    /**
     * 支付终端序列号
     */
    private String payDeviceSn;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AdminUser adminUser = (AdminUser) o;
        return Objects.equals(username, adminUser.username)
                && Objects.equals(realName, adminUser.realName)
                && Objects.equals(gender, adminUser.gender)
                && Objects.equals(email, adminUser.email)
                && Objects.equals(telephone, adminUser.telephone)
                && Objects.equals(avatar, adminUser.avatar)
                && Objects.equals(tollMan, adminUser.tollMan)
                && Objects.equals(billSerial, adminUser.billSerial)
                && Objects.equals(payDeviceSn, adminUser.payDeviceSn);
    }

    @Override
    public int hashCode() {
        return Objects.hash(username, realName, gender, email, telephone, avatar, tollMan, billSerial, payDeviceSn);
    }

    @Override
    public String toString() {
        return "AdminUser{" +
                "username='" + username + '\'' +
                ", realName='" + realName + '\'' +
                ", password='" + password + '\'' +
                ", salt='" + salt + '\'' +
                ", gender=" + gender +
                ", email='" + email + '\'' +
                ", telephone='" + telephone + '\'' +
                ", avatar='" + avatar + '\'' +
                ", departmentId=" + departmentId +
                ", tollMan=" + tollMan +
                ", maintainManType=" + maintainManType +
                ", loginPath='" + loginPath + '\'' +
                ", loginable=" + loginable +
                ", billSerial='" + billSerial + '\'' +
                ", payDeviceSn='" + payDeviceSn + '\'' +
                '}';
    }
}
