package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

/**
 * <AUTHOR>
 * @Date 2021/1/26 11:47
 */
public class AdminUserRole extends BaseEntity {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 角色id
     */
    private Long roleId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
}
