package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * 省市区实体
 * <AUTHOR>
 * @Date 2020/12/14 15:29
 */
public class Area extends BaseEntity {

    /**
     * 编号
     */
    private String serialNo;
    /**
     * 名称
     */
    private String name;
    /**
     * 简称
     */
    private String briefName;
    /**
     * 类型
     * @see com.senox.user.constant.AreaCategory
     */
    private Integer category;
    /**
     * 父id
     */
    private Long parentId;

    public Area() {
    }

    public Area(String name) {
        this.name = name;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBriefName() {
        return briefName;
    }

    public void setBriefName(String briefName) {
        this.briefName = briefName;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Area area = (Area) o;
        return Objects.equals(serialNo, area.serialNo)
                && Objects.equals(name, area.name)
                && Objects.equals(briefName, area.briefName)
                && Objects.equals(category, area.category)
                && Objects.equals(parentId, area.parentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(serialNo, name, briefName, category, parentId);
    }

    @Override
    public String toString() {
        return "Area{"
                + "serialNo='" + serialNo + '\''
                + ", name='" + name + '\''
                + ", briefName='" + briefName + '\''
                + ", category=" + category
                + ", parentId=" + parentId
                + '}';
    }
}
