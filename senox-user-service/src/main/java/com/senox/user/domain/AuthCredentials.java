package com.senox.user.domain;

import com.senox.common.domain.OperateEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 身份验证凭证
 * <AUTHOR>
 * @date 2023-8-29
 */
@Getter
@Setter
public class AuthCredentials extends OperateEntity {

    /**
     * 公钥
     */
    private String appKey;

    /**
     * 私钥
     */
    private String appSecret;

    /**
     * 用户id
     */
    private Long userId;

}
