package com.senox.user.domain;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/3/31 10:59
 */
public class BookingMeal {

    /**
     * id
     */
    private Long id;
    /**
     * 公众号id
     */
    private String mpid;
    /**
     * 用户openid
     */
    private String openid;
    /**
     * 企业
     */
    private String company;
    /**
     * 用户姓名
     */
    private String employee;
    /**
     * 代理公司
     */
    private Long delegateCompany;
    /**
     * 就餐日期
     */
    private LocalDate mealDate;
    /**
     * 是否预定就餐
     */
    private Integer mealBook;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMpid() {
        return mpid;
    }

    public void setMpid(String mpid) {
        this.mpid = mpid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getEmployee() {
        return employee;
    }

    public void setEmployee(String employee) {
        this.employee = employee;
    }

    public Long getDelegateCompany() {
        return delegateCompany;
    }

    public void setDelegateCompany(Long delegateCompany) {
        this.delegateCompany = delegateCompany;
    }

    public LocalDate getMealDate() {
        return mealDate;
    }

    public void setMealDate(LocalDate mealDate) {
        this.mealDate = mealDate;
    }

    public Integer getMealBook() {
        return mealBook;
    }

    public void setMealBook(Integer mealBook) {
        this.mealBook = mealBook;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BookingMeal that = (BookingMeal) o;
        return Objects.equals(company, that.company)
                && Objects.equals(employee, that.employee)
                && Objects.equals(mealDate, that.mealDate)
                && Objects.equals(mealBook, that.mealBook);
    }

    @Override
    public int hashCode() {
        return Objects.hash(company, employee, mealDate, mealBook);
    }
}
