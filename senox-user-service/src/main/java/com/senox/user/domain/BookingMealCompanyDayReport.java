package com.senox.user.domain;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 报餐公司日报
 * <AUTHOR>
 * @date 2021/4/16 8:33
 */
public class BookingMealCompanyDayReport {

    /**
     * id
     */
    private Long id;
    /**
     * 就餐日期
     */
    private LocalDate mealDate;
    /**
     * 企业
     */
    private String company;
    /**
     * 订餐人数
     */
    private Integer bookedCount;
    /**
     * 不订餐人数
     */
    private Integer unbookedCount;
    /**
     * 就餐年份
     */
    private Integer mealYear;
    /**
     * 就餐月份
     */
    private Integer mealMonth;
    /**
     * 是否总计
     */
    private Boolean total;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getMealDate() {
        return mealDate;
    }

    public void setMealDate(LocalDate mealDate) {
        this.mealDate = mealDate;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Integer getBookedCount() {
        return bookedCount;
    }

    public void setBookedCount(Integer bookedCount) {
        this.bookedCount = bookedCount;
    }

    public Integer getUnbookedCount() {
        return unbookedCount;
    }

    public void setUnbookedCount(Integer unbookedCount) {
        this.unbookedCount = unbookedCount;
    }

    public Integer getMealYear() {
        return mealYear;
    }

    public void setMealYear(Integer mealYear) {
        this.mealYear = mealYear;
    }

    public Integer getMealMonth() {
        return mealMonth;
    }

    public void setMealMonth(Integer mealMonth) {
        this.mealMonth = mealMonth;
    }

    public Boolean getTotal() {
        return total;
    }

    public void setTotal(Boolean total) {
        this.total = total;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BookingMealCompanyDayReport that = (BookingMealCompanyDayReport) o;
        return Objects.equals(mealDate, that.mealDate)
                && Objects.equals(company, that.company)
                && Objects.equals(bookedCount, that.bookedCount)
                && Objects.equals(unbookedCount, that.unbookedCount)
                && Objects.equals(total, that.total);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mealDate, company, bookedCount, unbookedCount, total);
    }

    @Override
    public String toString() {
        return "BookingMealCompanyDayReport{"
                + "id=" + id
                + ", mealDate=" + mealDate
                + ", company='" + company + '\''
                + ", bookedCount=" + bookedCount
                + ", unbookedCount=" + unbookedCount
                + ", mealYear=" + mealYear
                + ", mealMonth=" + mealMonth
                + ", total=" + total
                + ", createTime=" + createTime
                + ", modifiedTime=" + modifiedTime
                + '}';
    }
}
