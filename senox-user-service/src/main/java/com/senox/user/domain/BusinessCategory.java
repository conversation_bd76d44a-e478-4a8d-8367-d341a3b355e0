package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/10/8 10:52
 */

@Getter
@Setter
@ToString
@TableName("dict_business_category")
public class BusinessCategory extends TableIdEntity {

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;
    /**
     * 排序号
     */
    private Integer orderNum;
}
