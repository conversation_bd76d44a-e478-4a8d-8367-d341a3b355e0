package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/4/1 8:58
 */
public class Company extends BaseEntity {

    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 排序号
     */
    private Integer orderNo;


    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Company company = (Company) o;
        return Objects.equals(companyName, company.companyName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName);
    }

    @Override
    public String toString() {
        return "Company{"
                + "companyName='" + companyName + '\''
                + ", orderNo=" + orderNo
                + '}';
    }
}
