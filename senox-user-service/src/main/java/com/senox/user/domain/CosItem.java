package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * 权限
 * <AUTHOR>
 * @Date 2021/1/26 10:44
 */
public class CosItem extends BaseEntity {

    /**
     * 名称
     */
    private String name;
    /**
     * 权限展示名
     */
    private String displayName;
    /**
     * 路径
     */
    private String url;
    /**
     * 父权限
     */
    private Long parentId;
    /**
     * 排序号
     */
    private Integer orderNo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CosItem cosItem = (CosItem) o;
        return Objects.equals(name, cosItem.name)
                && Objects.equals(url, cosItem.url)
                && Objects.equals(parentId, cosItem.parentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, url, parentId);
    }

    @Override
    public String toString() {
        return "CosItem{"
                + "name='" + name + '\''
                + ", displayName='" + displayName + '\''
                + ", url='" + url + '\''
                + ", parentId=" + parentId
                + ", orderNo=" + orderNo
                + '}';
    }
}
