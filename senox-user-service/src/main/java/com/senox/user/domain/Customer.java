package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 客户
 * <AUTHOR>
 * @Date 2020/12/30 16:56
 */
public class Customer extends BaseEntity {

    /**
     * 编号
     */
    private String serialNo;
    /**
     * 客户名
     */
    private String name;
    /**
     * 身份证
     */
    private String idcard;
    /**
     * 证件类型
     */
    private Integer idcardType;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 民族
     */
    private String nation;
    /**
     * 出生日期
     */
    private LocalDate bornDate;
    /**
     * 籍贯
     */
    private String nativePlace;
    /**
     * 地址
     */
    private String address;
    /**
     * 手机号
     */
    private String telephone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 省/直辖市id
     */
    private Long provinceId;
    /**
     * 省/直辖市名
     */
    private String provinceName;
    /**
     * 市/区id
     */
    private Long cityId;
    /**
     * 市/区名
     */
    private String cityName;
    /**
     * 工作区域id
     */
    private Long workplaceRegionId;
    /**
     * 工作区域名
     */
    private String workplaceRegionName;
    /**
     * 工作街道id
     */
    private Long workplaceStreetId;
    /**
     * 工作街道名
     */
    private String workplaceStreetName;
    /**
     * 工作地址
     */
    private String workplaceAddress;
    /**
     * 行业id
     */
    private Long professionId;
    /**
     * 行业名
     */
    private String professionName;
    /**
     * 职位抬头
     */
    private String jobTitle;
    /**
     * 核酸检测
     */
    private Boolean natTested;
    /**
     * 新冠疫苗注射
     */
    private Boolean covid19Vaccination;
    /**
     * 中农网同步
     */
    private Boolean znwSynced;


    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public Integer getIdcardType() {
        return idcardType;
    }

    public void setIdcardType(Integer idcardType) {
        this.idcardType = idcardType;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public LocalDate getBornDate() {
        return bornDate;
    }

    public void setBornDate(LocalDate bornDate) {
        this.bornDate = bornDate;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Long getWorkplaceRegionId() {
        return workplaceRegionId;
    }

    public void setWorkplaceRegionId(Long workplaceRegionId) {
        this.workplaceRegionId = workplaceRegionId;
    }

    public Long getWorkplaceStreetId() {
        return workplaceStreetId;
    }

    public void setWorkplaceStreetId(Long workplaceStreetId) {
        this.workplaceStreetId = workplaceStreetId;
    }

    public String getWorkplaceStreetName() {
        return workplaceStreetName;
    }

    public void setWorkplaceStreetName(String workplaceStreetName) {
        this.workplaceStreetName = workplaceStreetName;
    }

    public String getWorkplaceRegionName() {
        return workplaceRegionName;
    }

    public void setWorkplaceRegionName(String workplaceRegionName) {
        this.workplaceRegionName = workplaceRegionName;
    }

    public String getWorkplaceAddress() {
        return workplaceAddress;
    }

    public void setWorkplaceAddress(String workplaceAddress) {
        this.workplaceAddress = workplaceAddress;
    }

    public Long getProfessionId() {
        return professionId;
    }

    public void setProfessionId(Long professionId) {
        this.professionId = professionId;
    }

    public String getProfessionName() {
        return professionName;
    }

    public void setProfessionName(String professionName) {
        this.professionName = professionName;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Boolean getNatTested() {
        return natTested;
    }

    public void setNatTested(Boolean natTested) {
        this.natTested = natTested;
    }

    public Boolean getCovid19Vaccination() {
        return covid19Vaccination;
    }

    public void setCovid19Vaccination(Boolean covid19Vaccination) {
        this.covid19Vaccination = covid19Vaccination;
    }

    public Boolean getZnwSynced() {
        return znwSynced;
    }

    public void setZnwSynced(Boolean znwSynced) {
        this.znwSynced = znwSynced;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Customer customer = (Customer) o;
        return Objects.equals(serialNo, customer.serialNo)
                && Objects.equals(name, customer.name)
                && Objects.equals(idcard, customer.idcard)
                && Objects.equals(idcardType, customer.idcardType)
                && Objects.equals(gender, customer.gender)
                && Objects.equals(nation, customer.nation)
                && Objects.equals(bornDate, customer.bornDate)
                && Objects.equals(nativePlace, customer.nativePlace)
                && Objects.equals(address, customer.address)
                && Objects.equals(telephone, customer.telephone)
                && Objects.equals(email, customer.email)
                && Objects.equals(provinceId, customer.provinceId)
                && Objects.equals(provinceName, customer.provinceName)
                && Objects.equals(cityId, customer.cityId)
                && Objects.equals(cityName, customer.cityName)
                && Objects.equals(workplaceRegionId, customer.workplaceRegionId)
                && Objects.equals(workplaceRegionName, customer.workplaceRegionName)
                && Objects.equals(workplaceStreetId, customer.workplaceStreetId)
                && Objects.equals(workplaceStreetName, customer.workplaceStreetName)
                && Objects.equals(workplaceAddress, customer.workplaceAddress)
                && Objects.equals(professionId, customer.professionId)
                && Objects.equals(professionName, customer.professionName)
                && Objects.equals(jobTitle, customer.jobTitle)
                && Objects.equals(natTested, customer.natTested)
                && Objects.equals(covid19Vaccination, customer.covid19Vaccination)
                ;
    }

    @Override
    public int hashCode() {
        return Objects.hash(serialNo, name, idcard, idcardType, gender, nation, bornDate, nativePlace, address, telephone
                , email, provinceId, provinceName, cityId, cityName, workplaceRegionId, workplaceRegionName
                , workplaceStreetId, workplaceStreetName, workplaceAddress, professionId, professionName
                , jobTitle, natTested, covid19Vaccination);
    }

    @Override
    public String toString() {
        return "Customer{"
                + "serialNo='" + serialNo + '\''
                + ", name='" + name + '\''
                + ", idcard='" + idcard + '\''
                + ", idcardType=" + idcardType
                + ", gender=" + gender
                + ", nation='" + nation + '\''
                + ", bornDate=" + bornDate
                + ", nativePlace='" + nativePlace + '\''
                + ", address='" + address + '\''
                + ", telephone='" + telephone + '\''
                + ", provinceId=" + provinceId
                + ", provinceName='" + provinceName + '\''
                + ", cityId=" + cityId
                + ", cityName='" + cityName + '\''
                + ", email='" + email + '\''
                + ", workplaceRegionId=" + workplaceRegionId
                + ", workplaceRegionName='" + workplaceRegionName + '\''
                + ", workplaceStreetId=" + workplaceStreetId
                + ", workplaceStreetName='" + workplaceStreetName + '\''
                + ", workplaceAddress='" + workplaceAddress + '\''
                + ", professionId=" + professionId
                + ", professionName='" + professionName + '\''
                + ", jobTitle='" + jobTitle + '\''
                + ", natTested=" + natTested
                + ", covid19Vaccination=" + covid19Vaccination
                + ", znwSynced=" + znwSynced
                + '}';
    }
}
