package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/1/20 11:52
 */
public class CustomerCovid19 extends BaseEntity {

    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 类型
     */
    private Integer category;
    /**
     * 操作日期
     */
    private LocalDate operateDate;
    /**
     * 备注
     */
    private String remark;

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public LocalDate getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(LocalDate operateDate) {
        this.operateDate = operateDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CustomerCovid19 that = (CustomerCovid19) o;
        return Objects.equals(customerId, that.customerId)
                && Objects.equals(category, that.category)
                && Objects.equals(operateDate, that.operateDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customerId, category, operateDate);
    }
}
