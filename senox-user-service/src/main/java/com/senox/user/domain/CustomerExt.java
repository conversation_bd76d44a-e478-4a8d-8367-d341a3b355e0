package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

/**
 * <AUTHOR>
 * @Date 2021/1/19 15:15
 */
public class CustomerExt extends BaseEntity {

    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 银行账号
     */
    private String bankAccount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 头像
     */
    private String avatar;

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    @Override
    public String toString() {
        return "CustomerExt{"
                + "customerId=" + customerId
                + ", bankAccount='" + bankAccount + '\''
                + ", remark='" + remark + '\''
                + ", avatar='" + avatar + '\''
                + '}';
    }
}
