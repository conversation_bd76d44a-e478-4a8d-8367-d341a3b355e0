package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * 部门
 * <AUTHOR>
 * @date 2021/9/15 10:33
 */
public class Department extends BaseEntity {

    /**
     * 部门名
     */
    private String name;
    /**
     * 部门全称
     */
    private String fullName;
    /**
     * 父部门id
     */
    private Long parentId;
    /**
     * 排序
     */
    private Integer orderNo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Department that = (Department) o;
        return Objects.equals(name, that.name)
                && Objects.equals(fullName, that.fullName)
                && Objects.equals(parentId, that.parentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, fullName, parentId);
    }

    @Override
    public String toString() {
        return "Department{"
                + "name='" + name + '\''
                + ", fullName='" + fullName + '\''
                + ", parentId=" + parentId
                + ", orderNo=" + orderNo
                + '}';
    }
}
