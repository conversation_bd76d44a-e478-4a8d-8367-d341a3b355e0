package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Objects;

/**
 * 就餐信息
 */
@Getter
@Setter
@ToString
@TableName("u_dining_information")
public class DiningInformation extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -7315196579701600705L;

    /**
     * 编号
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户
      */
    private String employeeName;
    /**
     * 就餐日期
     */
    private LocalDate mealDate;
    /**
     * 就餐时间
     */
    private LocalTime mealTime;
    /**
     * 是否就餐
     */
    private Integer  dining;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DiningInformation that = (DiningInformation) o;
        return Objects.equals(employeeName, that.employeeName)
                && Objects.equals(mealDate, that.mealDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(employeeName, mealDate);
    }
}
