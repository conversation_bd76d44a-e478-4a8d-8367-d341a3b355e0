package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * 员工
 * <AUTHOR>
 * @date 2021/4/1 11:28
 */
public class Employee extends BaseEntity {

    /**
     * 姓名
     */
    private String username;
    /**
     * 企业名
     */
    private String companyName;

    /**
     * 部门id
     */
    private Long departmentId;
    /**
     * 公众号绑定的微信id
     */
    private String mpOpenid;
    /**
     * 默认报餐
     */
    private Integer defaultBooked;
    /**
     * 餐厅管理员
     */
    private Boolean canteenMaster;
    /**
     * 备注
     */
    private String remark;


    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getMpOpenid() {
        return mpOpenid;
    }

    public void setMpOpenid(String mpOpenid) {
        this.mpOpenid = mpOpenid;
    }

    public Integer getDefaultBooked() {
        return defaultBooked;
    }

    public void setDefaultBooked(Integer defaultBooked) {
        this.defaultBooked = defaultBooked;
    }

    public Boolean getCanteenMaster() {
        return canteenMaster;
    }

    public void setCanteenMaster(Boolean canteenMaster) {
        this.canteenMaster = canteenMaster;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Employee employee = (Employee) o;
        return Objects.equals(username, employee.username)
                && Objects.equals(companyName, employee.companyName)
                && Objects.equals(defaultBooked, employee.defaultBooked)
                && Objects.equals(mpOpenid, employee.mpOpenid)
                && Objects.equals(canteenMaster, employee.canteenMaster);
    }

    @Override
    public int hashCode() {
        return Objects.hash(username, companyName, mpOpenid, defaultBooked, canteenMaster);
    }

    @Override
    public String toString() {
        return "Employee{"
                + "username='" + username + '\''
                + ", companyName='" + companyName + '\''
                + ", mpOpenid='" + mpOpenid + '\''
                + ", defaultBooked=" + defaultBooked
                + ", remark='" + remark + '\''
                + ", canteenMaster=" + canteenMaster
                + '}';
    }
}
