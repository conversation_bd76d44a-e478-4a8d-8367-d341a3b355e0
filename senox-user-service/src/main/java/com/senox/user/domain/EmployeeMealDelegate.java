package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;
import com.senox.user.constant.DelegateType;

import java.util.Objects;

/**
 * 员工订餐代理
 * <AUTHOR>
 * @date 2021/4/1 16:43
 */
public class EmployeeMealDelegate extends BaseEntity {

    /**
     * 员工id
     */
    private Long employeeId;
    /**
     * 代理id
     */
    private Long delegateId;
    /**
     * 代理类型
     * @see DelegateType
     */
    private Integer type;

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public Long getDelegateId() {
        return delegateId;
    }

    public void setDelegateId(Long delegateId) {
        this.delegateId = delegateId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EmployeeMealDelegate that = (EmployeeMealDelegate) o;
        return Objects.equals(employeeId, that.employeeId)
                && Objects.equals(delegateId, that.delegateId)
                && Objects.equals(type, that.type)
                ;
    }

    @Override
    public int hashCode() {
        return Objects.hash(employeeId, delegateId, type);
    }

    @Override
    public String toString() {
        return "EmployeeMealDelegate{"
                + "employeeId=" + employeeId
                + ", delegateId=" + delegateId
                + ", type=" + type
                + '}';
    }
}
