package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/8/9 10:41
 */
@Getter
@Setter
@ToString
@TableName("u_enterprise")
public class Enterprise extends TableIdEntity {

    /**
     * 企业名
     */
    private String name;
    /**
     * 营业执照全名
     */
    private String fullName;
    /**
     * 负责人
     */
    private String chargeMan;
    /**
     * 联系方式1
     */
    private String contact1;
    /**
     * 联系方式2
     */
    private String contact2;
    /**
     * 经营范围
     */
    private String category;
    /**
     * 其他经营范围
     */
    private String otherCategory;
    /**
     * 重点消防场所
     */
    @TableField("is_firefighting_emphasis")
    private Boolean firefightingEmphasis;
    /**
     * 地址
     */
    private String address;
    /**
     * 备注
     */
    private String remark;
}
