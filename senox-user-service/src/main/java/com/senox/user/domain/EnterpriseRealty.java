package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/9 10:54
 */
@Getter
@Setter
@ToString
@TableName("u_enterprise_realty")
public class EnterpriseRealty extends TableIdEntity {

    /**
     * 企业id
     */
    private Long enterpriseId;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 物业别名（拆分转租）
     */
    private String realtyAlias;

    public EnterpriseRealty() {
    }

    public EnterpriseRealty(Long enterpriseId, String realtySerial) {
        this.enterpriseId = enterpriseId;
        this.realtySerial = realtySerial;
        this.realtyAlias = "";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EnterpriseRealty that = (EnterpriseRealty) o;
        return Objects.equals(enterpriseId, that.enterpriseId)
                && Objects.equals(realtySerial, that.realtySerial);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enterpriseId, realtySerial);
    }
}
