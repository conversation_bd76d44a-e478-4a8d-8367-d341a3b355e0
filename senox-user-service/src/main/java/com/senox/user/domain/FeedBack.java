package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/5/31 16:39
 */
@Getter
@Setter
@ToString
@TableName("u_feed_back")
public class FeedBack extends BaseEntity {

    private static final long serialVersionUID = 741414162738877674L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 微信用户id
     */
    private String openid;
    /**
     * 标题
     */
    private String title;
    /**
     * 是否匿名
     */
    private Boolean anonymous;
    /**
     * 姓名
     */
    private String name;
    /**
     * 联系方式
     */
    private String contact;
    /**
     * 反馈内容
     */
    private String content;
    /**
     * 回复状态
     */
    private Boolean replyState;
}
