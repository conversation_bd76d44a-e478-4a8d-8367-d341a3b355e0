package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/9 16:10
 */
@Getter
@Setter
@ToString
@TableName("u_feed_back_media")
public class FeedBackMedia {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 意见反馈id
     */
    private Long feedBackId;
    /**
     * 多媒体路径
     */
    private String mediaUrl;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;


    public FeedBackMedia() {
    }

    public FeedBackMedia(Long feedBackId, String mediaUrl) {
        this.feedBackId = feedBackId;
        this.mediaUrl = mediaUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FeedBackMedia that = (FeedBackMedia) o;
        return Objects.equals(feedBackId, that.feedBackId)
                && Objects.equals(mediaUrl, that.mediaUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(feedBackId, mediaUrl);
    }

}
