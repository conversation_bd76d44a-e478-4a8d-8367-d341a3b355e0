package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/6/1 9:25
 */
@Getter
@Setter
@ToString
@TableName("u_feed_back_reply")
public class FeedBackReply extends BaseEntity {
    private static final long serialVersionUID = 741414162738877674L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 微信用户id
     */
    private String openid;
    /**
     * 建议评论id
     */
    private Long feedBackId;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 姓名
     */
    private String name;
    /**
     * 父级评论
     */
    private Long parentId;
}
