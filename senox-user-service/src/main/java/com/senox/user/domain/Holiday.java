package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 假期
 * <AUTHOR>
 * @date 2021/4/19 8:38
 */
public class Holiday extends BaseEntity {

    /**
     * 假期日期
     */
    private LocalDate holiday;
    /**
     * 描述
     */
    private String description;


    public LocalDate getHoliday() {
        return holiday;
    }

    public void setHoliday(LocalDate holiday) {
        this.holiday = holiday;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Holiday holiday1 = (Holiday) o;
        return Objects.equals(holiday, holiday1.holiday);
    }

    @Override
    public int hashCode() {
        return Objects.hash(holiday);
    }

    @Override
    public String toString() {
        return "Holiday{"
                + "holiday=" + holiday
                + ", description='" + description + '\''
                + '}';
    }
}
