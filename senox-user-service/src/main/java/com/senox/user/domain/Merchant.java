package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.user.constant.MerchantBillSettlePeriod;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-10-30
 */
@Setter
@Getter
@TableName("u_merchant")
public class Merchant extends TableIdEntity {

    /**
     * 商户名
     */
    private String name;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 身份证
     */
    private String idcard;

    /**
     * 通讯地址
     */
    private String address;

    /**
     * 冷藏客户编号
     */
    private String rcSerial;

    /**
     * 冷藏抬头
     */
    private String rcTaxHeader;

    /**
     * 多多客户
     */
    @TableField("is_duoduo")
    private Boolean duoduo;

    /**
     * 结算周期
     *
     * @see MerchantBillSettlePeriod
     */
    private Integer settlePeriod;

    /**
     * 三轮车配送权限
     */
    private Boolean bicycleAuth;

    /**
     * 干仓权限
     */
    private Boolean dryAuth;

    /**
     * 推荐码
     */
    private String referralCode;

    /**
     * 三轮车配送收费标准id
     */
    private Long bicycleChargesId;

    /**
     * 备注
     */
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Merchant merchant = (Merchant) o;
        return Objects.equals(name, merchant.name)
                && Objects.equals(contact, merchant.contact)
                && Objects.equals(idcard, merchant.idcard)
                && Objects.equals(rcSerial, merchant.rcSerial);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, contact, idcard, rcSerial);
    }
}
