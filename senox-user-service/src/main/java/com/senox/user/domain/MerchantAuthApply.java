package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Setter
@Getter
@TableName("u_merchant_auth_apply")
public class MerchantAuthApply extends TableIdEntity {

    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 商户姓名
     */
    private String merchantName;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 身份证
     */
    private String idcard;

    /**
     * 地址
     */
    private String address;

    /**
     * 冷藏客户编号
     */
    private String rcSerial;

    /**
     * 三轮车配送权限
     */
    private Boolean bicycleAuth;

    /**
     * 干仓权限
     */
    private Boolean dryAuth;

    /**
     * 推荐码
     */
    private String referralCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 审核人
     */
    private Integer auditStatus;

    /**
     * 审核意见
     */
    private String auditRemark;

    /**
     * 审核人
     */
    private Long auditId;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 申请人openid
     */
    private String createOpenid;
}
