package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/10 13:41
 */
@TableName("u_prize")
@Getter
@Setter
public class Prize extends TableIdEntity {

    /**
     * 抽奖活动id
     */
    private Long activityId;

    /**
     * 奖品名称
     */
    private String name;

    /**
     * 奖品描述
     */
    private String description;

    /**
     * 媒体资源
     */
    private String mediaUrl;

    /**
     * 奖品总数量
     */
    private Integer totalNum;

    /**
     * 剩余奖品数量
     */
    private Integer remainingNum;

    /**
     * 中奖概率(0-100)
     */
    private Integer probability;
}
