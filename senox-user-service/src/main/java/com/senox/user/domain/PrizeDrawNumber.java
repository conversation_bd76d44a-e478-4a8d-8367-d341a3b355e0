package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.user.constant.PrizeDrawNumberType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/15 11:05
 */
@TableName("u_prize_draw_number")
@Data
public class PrizeDrawNumber {

    @TableId(
            type = IdType.AUTO
    )
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * openid
     */
    private String openid;

    /**
     * 获取票数方式
     * @see PrizeDrawNumberType
     */
    private Integer type;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}
