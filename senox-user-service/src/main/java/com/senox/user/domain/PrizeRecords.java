package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/11 10:46
 */
@TableName("u_prize_records")
@Getter
@Setter
public class PrizeRecords extends TableIdEntity {

    /**
     * uuid
     */
    private String uuid;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 奖品id，NULL表示未中奖
     */
    private Long prizeId;

    /**
     * 奖品名
     */
    private String prizeName;

    /**
     * openid
     */
    private String openid;

    /**
     * 是否中奖
     */
    private Boolean isWin;

    /**
     * 是否核销
     */
    private Boolean isVerify;
}
