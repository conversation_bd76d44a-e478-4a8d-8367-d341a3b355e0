package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * 行业
 * <AUTHOR>
 * @Date 2020/12/31 14:58
 */
public class Profession extends BaseEntity {

    /**
     * 行业名
     */
    private String name;

    public Profession() {
    }

    public Profession(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Profession that = (Profession) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }

    @Override
    public String toString() {
        return "Profession{"
                + "name='" + name + '\''
                + '}';
    }
}
