package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/27 16:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("u_reservation_record")
public class ReservationRecord extends TableIdEntity {

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 访客电话
     */
    private String contact;

    /**
     * 随行人数
     */
    private Integer togetherNum;

    /**
     * 预约类型
     * @see com.senox.user.constant.ReservationType
     */
    private Integer type;

    /**
     * 拜访时间起
     */
    private LocalDateTime visitTimeStart;

    /**
     * 拜访时间止
     */
    private LocalDateTime visitTimeEnd;

    /**
     * 申请人openid
     */
    private String createOpenid;
}
