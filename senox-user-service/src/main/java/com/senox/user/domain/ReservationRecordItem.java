package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/27 16:59
 */
@Data
@TableName("u_reservation_record_item")
public class ReservationRecordItem extends TableIdEntity {

    /**
     * 预约记录id
     */
    private Long reservationRecordId;

    /**
     * 车牌
     */
    private String carNo;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        ReservationRecordItem recordItem = (ReservationRecordItem) obj;
        return Objects.equals(reservationRecordId, recordItem.getReservationRecordId())
                && Objects.equals(carNo, recordItem.getCarNo());
    }

    @Override
    public int hashCode() {
        return Objects.hash(reservationRecordId, carNo);
    }
}
