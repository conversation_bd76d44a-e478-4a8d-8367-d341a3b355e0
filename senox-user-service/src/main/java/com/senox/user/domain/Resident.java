package com.senox.user.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/5/12 14:51
 */
@Getter
@Setter
@ToString
@TableName("u_resident")
public class Resident extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 唯一编号
     */
    private String residentNo;
    /**
     * 住户类型 0 住户 1 员工 2 其他
     */
    private Integer residentType;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String idNum;
    /**
     * 出生日期
     */
    private String bornDate;
    /**
     * 性别 1 男 2 女
     */
    private Integer gender;
    /**
     * 民族
     */
    private String nature;
    /**
     * 电话号码
     */
    private String telephone;
    /**
     * 具体住址
     */
    private String address;
    /**
     * 人脸
     */
    private String faceUrl;
    /**
     * 备注
     */
    private String remark;
}
