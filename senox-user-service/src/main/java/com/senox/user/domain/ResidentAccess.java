package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/12 17:06
 */
@Getter
@Setter
@ToString
@TableName("u_resident_access")
public class ResidentAccess extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 住户编号
     */
    private String residentNo;
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 物业编号
     */
    private String realtySerial;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 拥有权限
     */
    private Boolean access;
    /**
     * 是否生效（0：未生效，1：已生效）
     */
    private Boolean state;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ResidentAccess that = (ResidentAccess) o;
        return Objects.equals(residentNo, that.residentNo)
                && Objects.equals(deviceId, that.deviceId)
                && Objects.equals(realtySerial, that.realtySerial)
                && Objects.equals(contractNo, that.contractNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(residentNo, deviceId, realtySerial, contractNo);
    }
}
