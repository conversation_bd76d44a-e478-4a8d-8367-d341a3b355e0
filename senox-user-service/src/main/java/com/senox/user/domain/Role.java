package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 角色
 * <AUTHOR>
 * @Date 2021/1/26 10:48
 */
@Getter
@Setter
public class Role extends BaseEntity {

    /**
     * 角色名
     */
    private String name;

    /**
     * 角色编码
     */
    private String code;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Role role = (Role) o;
        return Objects.equals(getName(), role.getName()) && Objects.equals(getCode(), role.getCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName(), getCode());
    }

    @Override
    public String toString() {
        return "Role{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
