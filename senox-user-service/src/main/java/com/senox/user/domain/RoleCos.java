package com.senox.user.domain;

import com.senox.common.domain.BaseEntity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/1/26 10:58
 */
public class RoleCos extends BaseEntity {

    /**
     * 角色id
     */
    private Long roleId;
    /**
     * 权限id
     */
    private Long cosId;

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getCosId() {
        return cosId;
    }

    public void setCosId(Long cosId) {
        this.cosId = cosId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RoleCos roleCos = (RoleCos) o;
        return Objects.equals(roleId, roleCos.roleId)
                && Objects.equals(cosId, roleCos.cosId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roleId, cosId);
    }

    @Override
    public String toString() {
        return "RoleCos{"
                + "roleId=" + roleId
                + ", cosId=" + cosId
                + '}';
    }
}
