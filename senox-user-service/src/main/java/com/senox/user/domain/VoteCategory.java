package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/16 9:59
 */
@TableName("u_vote_category")
@Data
public class VoteCategory {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 类别名
     */
    private String name;

    /**
     * 活动Id
     */
    private Long activityId;

    /**
     * 禁用
     */
    @TableField("is_disabled")
    private Boolean disabled;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}
