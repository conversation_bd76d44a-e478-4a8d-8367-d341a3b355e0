package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:41
 */
@TableName("u_vote_records")
@Getter
@Setter
public class VoteRecords extends TableIdEntity {

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 资源id
     */
    private Long resourcesId;

    /**
     * openid
     */
    private String openid;

    /**
     * 票数
     */
    private Integer numbers;

    /**
     * 备注
     */
    private String remark;
}
