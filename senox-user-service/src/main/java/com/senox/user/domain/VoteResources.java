package com.senox.user.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:30
 */
@TableName("u_vote_resources")
@Getter
@Setter
public class VoteResources extends TableIdEntity {

    /**
     * 投票活动id
     */
    private Long activityId;

    /**
     * 名称
     */
    private String name;

    /**
     * 编号
     */
    private Integer serial;

    /**
     * 类别
     */
    private Integer category;

    /**
     * 简介
     */
    private String description;

    /**
     * 票数
     */
    private Integer numbers;

    /**
     * 缩略图
     */
    private String thumbnail;

    /**
     * 原图
     */
    private String original;
}
