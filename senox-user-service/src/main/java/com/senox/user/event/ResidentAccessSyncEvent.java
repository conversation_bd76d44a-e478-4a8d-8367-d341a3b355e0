package com.senox.user.event;

import com.senox.user.vo.ResidentAccessVo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/10/25 14:14
 */
@Getter
@Setter
public class ResidentAccessSyncEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1356791258214146810L;

    private List<ResidentAccessVo> accessVoList;


    public ResidentAccessSyncEvent(Object source, List<ResidentAccessVo> accessVoList) {
        super(source);
        this.accessVoList = accessVoList;
    }
}
