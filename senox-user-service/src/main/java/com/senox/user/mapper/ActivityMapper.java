package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.Activity;
import com.senox.user.vo.ActivitySearchVo;
import com.senox.user.vo.ActivityVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 8:22
 */
@Mapper
public interface ActivityMapper extends BaseMapper<Activity> {

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int count(ActivitySearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<ActivityVo> list(ActivitySearchVo searchVo);
}
