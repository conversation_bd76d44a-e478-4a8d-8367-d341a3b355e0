package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.AdminRemoteAccess;
import com.senox.user.vo.AdminRemoteAccessSearchVo;
import com.senox.user.vo.AdminRemoteAccessVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/26 8:37
 */
@Mapper
public interface AdminRemoteAccessMapper extends BaseMapper<AdminRemoteAccess> {

    /**
     * 根据id查询远程开门权限信息
     *
     * @param id
     * @return
     */
    AdminRemoteAccessVo findRemoteAccessById(Long id);

    /**
     * 远程开门权限count
     *
     * @param search
     * @return
     */
    int count(AdminRemoteAccessSearchVo search);

    /**
     * 远程开门权限列表
     *
     * @param search
     * @return
     */
    List<AdminRemoteAccessVo> list(AdminRemoteAccessSearchVo search);

}
