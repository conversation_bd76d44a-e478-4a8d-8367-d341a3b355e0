package com.senox.user.mapper;

import com.senox.user.domain.AdminUser;
import com.senox.user.domain.AdminUserRole;
import com.senox.user.vo.AdminUserSearchVo;
import com.senox.user.vo.AdminUserVo;
import com.senox.user.vo.TollManSerialVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/28 14:24
 */
@Mapper
@Repository
public interface AdminUserMapper {

    /**
     * 添加管理原信息
     * @param adminUser
     * @return
     */
    int addAdminUser(AdminUser adminUser);

    /**
     * 更新管理用户信息
     * @param adminUser
     * @return
     */
    int updateAdminUser(AdminUser adminUser);

    /**
     * 获取票据编号
     * @param id
     * @return
     */
    TollManSerialVo findBillSerial(Long id);

    /**
     * 根据id查找用户
     * @param id
     * @return
     */
    AdminUserVo findById(Long id);

    /**
     * 根据openid查找管理用户
     * @param appId
     * @param openid
     * @return
     */
    AdminUser findByOpenid(@Param("appId") String appId, @Param("openid") String openid);

    /**
     * 根据用户名查找用户
     * @param username
     * @return
     */
    AdminUser findByUsername(String username);

    /**
     * 根据真实姓名查找用户
     * @param realName
     * @return
     */
    AdminUser findByRealName(String realName);

    /**
     * 根据手机号查找用户
     * @param telephone
     * @return
     */
    AdminUser findByTelephone(String telephone);

    /**
     * 管理用户统计
     * @param searchVo
     * @return
     */
    int countAdminUser(AdminUserSearchVo searchVo);

    /**
     * 管理用户列表
     * @param searchVo
     * @return
     */
    List<AdminUserVo> listAdminUser(AdminUserSearchVo searchVo);


    /**
     * 批量添加用户角色
     * @param userRoles
     * @return
     */
    int batchAddAdminUserRole(@Param("userRoles") List<AdminUserRole> userRoles);

    /**
     * 批量删除用户角色
     * @param userId
     * @param roles
     * @return
     */
    int batchDelAdminUserRole(@Param("userId") Long userId, @Param("roles") List<Long> roles);

    /**
     * 判断角色占用情况
     * @param roleId
     * @return
     */
    int countAdminUserRole(Long roleId);

    /**
     * 用户角色列表
     * @param userId
     * @return
     */
    List<Long> listAdminUserRole(Long userId);

    /**
     * 用户角色编码列表
     * @param userId
     * @return
     */
    List<String> listAdminUserRoleCode(Long userId);

    /**
     * 角色用户列表
     * @param roleId
     * @return
     */
    List<AdminUserVo> listRoleAdminUser(Long roleId);

    /**
     * 批量添加用户部门
     * @param userId
     * @param departmentIds
     * @return
     */
    int batchAddUserDepartment(@Param("userId") Long userId, @Param("departmentIds") List<Long> departmentIds);

    /**
     * 批量删除用户部门
     * @param userId
     * @param departmentIds
     * @return
     */
    int batchDelUserDepartment(@Param("userId") Long userId, @Param("departmentIds") List<Long> departmentIds);

    /**
     * 用户部门id列表
     * @param userId
     * @return
     */
    List<Long> listUserDepartmentId(Long userId);
}
