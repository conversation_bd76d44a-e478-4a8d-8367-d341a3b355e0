package com.senox.user.mapper;

import com.senox.user.domain.Area;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/15 7:59
 */
@Mapper
@Repository
public interface AreaMapper {

    /**
     * 添加地区
     * @param area
     * @return
     */
    int addArea(Area area);

    /**
     * 更新地区
     * @param area
     * @return
     */
    int updateArea(Area area);

    /**
     * 查找地区
     * @param id
     * @return
     */
    Area findById(Long id);

    /**
     * 根据编号查找id
     * @param serialNo
     * @return
     */
    Long findIdBySerialNo(String serialNo);

    /**
     * 查找地区子列表
     * @param parentId
     * @param categories
     * @return
     */
    List<Area> listByParentIdAndCategory(@Param("parentId") Long parentId, @Param("categories") List<Integer> categories);

    /**
     * 区域列表
     * @return
     */
    List<Area> listArea();
}
