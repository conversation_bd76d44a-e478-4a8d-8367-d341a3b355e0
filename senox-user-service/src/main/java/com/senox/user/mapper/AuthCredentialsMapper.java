package com.senox.user.mapper;

import com.senox.user.domain.AuthCredentials;
import com.senox.user.vo.AuthCredentialsSearchVo;
import com.senox.user.vo.AuthCredentialsVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-8-29
 */
public interface AuthCredentialsMapper {

    /**
     * 根据appKey查询身份验证凭证
     *
     * @param appKey appKey
     * @return 返回查询到的身份验证凭证
     */
    AuthCredentialsVo getByAppKey(String appKey);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<AuthCredentialsVo> list(AuthCredentialsSearchVo searchVo);

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    int countList(AuthCredentialsSearchVo searchVo);

    /**
     * 添加凭证
     *
     * @param newAuthCredentials 凭证
     * @return 返回1为添加成功
     */
    int add(AuthCredentials newAuthCredentials);

}
