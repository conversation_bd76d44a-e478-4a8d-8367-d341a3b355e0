package com.senox.user.mapper;

import com.senox.user.domain.BookingMealCompanyDayReport;
import com.senox.user.vo.BookingMealDayReportSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/16 8:37
 */
@Mapper
@Repository
public interface BookingMealCompanyDayReportMapper {

    /**
     * 批量添加报餐公司日报
     * @param dayReportList
     * @return
     */
    int batchAddDayReport(@Param("dayReportList") List<BookingMealCompanyDayReport> dayReportList);

    /**
     * 批量更新报餐公司日报
     * @param dayReportList
     * @return
     */
    int batchUpdateDayReport(@Param("dayReportList") List<BookingMealCompanyDayReport> dayReportList);

    /**
     * 批量删除日报
     * @param idList
     * @return
     */
    int batchDeleteDayReport(@Param("idList") List<Long> idList);

    /**
     * 根据订餐日期加载日报
     * @param dateList
     * @return
     */
    List<BookingMealCompanyDayReport> listDayReportByMealDate(@Param("dateList") List<LocalDate> dateList);

    /**
     * 报餐日报统计
     * @param searchVo
     * @return
     */
    int countDayReport(BookingMealDayReportSearchVo searchVo);

    /**
     * 报餐日报列表
     * @param searchVo
     * @return
     */
    List<BookingMealCompanyDayReport> listDayReport(BookingMealDayReportSearchVo searchVo);

    /**
     * 查看日报详情
     * @param mealDate
     * @return
     */
    List<BookingMealCompanyDayReport> listDayReportDetail(LocalDate mealDate);
}
