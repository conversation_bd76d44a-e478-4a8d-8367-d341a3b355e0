package com.senox.user.mapper;

import com.senox.user.domain.BookingMeal;
import com.senox.user.domain.BookingMealCompanyDayReport;
import com.senox.user.vo.BookingMealDayReportSearchVo;
import com.senox.user.vo.BookingMealSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/6 9:34
 */
@Mapper
@Repository
public interface BookingMealMapper {

    /**
     * 统计订餐数
     * @param searchVo
     * @return
     */
    int countBooking(BookingMealSearchVo searchVo);

    /**
     * 订餐列表
     * @param searchVo
     * @return
     */
    List<BookingMeal> listBooking(BookingMealSearchVo searchVo);

    /**
     * 代公司报餐
     * @param searchVo
     * @return
     */
    List<BookingMealCompanyDayReport> listDelegateBooking(BookingMealDayReportSearchVo searchVo);

    /**
     * 批量添加就餐预定
     * @param bookingList
     * @return
     */
    int batchAddBookings(@Param("bookingList") List<BookingMeal> bookingList);

    /**
     * 公司日报
     * @param searchVo
     * @return
     */
    List<BookingMealCompanyDayReport> listCompanyDayReport(BookingMealDayReportSearchVo searchVo);
}
