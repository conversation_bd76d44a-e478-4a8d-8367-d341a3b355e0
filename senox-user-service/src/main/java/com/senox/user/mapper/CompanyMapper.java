package com.senox.user.mapper;

import com.senox.user.domain.Company;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 9:00
 */
@Mapper
@Repository
public interface CompanyMapper {

    /**
     * 添加企业
     * @param company
     * @return
     */
    int addCompany(Company company);

    /**
     * 更新企业
     * @param company
     * @return
     */
    int updateCompany(Company company);

    /**
     * 根据id查找企业
     * @param id
     * @return
     */
    Company findById(Long id);

    /**
     * 根据名称查找企业
     * @param name
     * @return
     */
    Company findByName(String name);

    /**
     * 企业列表
     * @return
     */
    List<Company> listAll();

    /**
     * 代理报餐的企业
     * @return
     */
    List<Company> listDelegateCompany();

    /**
     * 未报餐的代理企业
     * @param mealDate
     * @return
     */
    List<Company> listNoBookedDelegateCompany(LocalDate mealDate);
}
