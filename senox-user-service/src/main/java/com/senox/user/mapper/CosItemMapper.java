package com.senox.user.mapper;

import com.senox.user.domain.CosItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/26 11:51
 */
@Mapper
@Repository
public interface CosItemMapper {

    /**
     * 添加权限项
     * @param cosItem
     * @return
     */
    int addCosItem(CosItem cosItem);

    /**
     * 更新权限项
     * @param cosItem
     * @return
     */
    int updateCosItem(CosItem cosItem);

    /**
     * 删除权限项
     * @param id
     * @return
     */
    int deleteCosItem(Long id);

    /**
     * 根据id获取权限项
     * @param id
     * @return
     */
    CosItem findById(Long id);

    /**
     * 根据权限名获取id
     * @param name
     * @return
     */
    Long findIdByName(String name);

    /**
     * 权限列表
     * @return
     */
    List<CosItem> listAll();

    /**
     * 根据角色查找权限列表
     * @param roles
     * @return
     */
    List<CosItem> listCosByRole(@Param("roles") List<Long> roles);

}
