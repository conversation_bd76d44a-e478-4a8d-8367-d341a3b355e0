package com.senox.user.mapper;

import com.senox.user.domain.CustomerCovid19;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/20 11:58
 */
@Mapper
@Repository
public interface CustomerCovid19Mapper {

    /**
     * 客户新冠防控记录
     * @param covid19
     * @return
     */
    int addCustomerCovid19(CustomerCovid19 covid19);

    /**
     * 批量添加客户新冠防控记录
     * @param covid19List
     * @return
     */
    int batchAddCustomerCovid19(@Param("covid19List") List<CustomerCovid19> covid19List);

    /**
     * 批量删除客户新冠防控记录
     * @param customerId
     * @param ids
     * @return
     */
    int deleteCustomerCovid19(@Param("customerId") Long customerId, @Param("ids") List<Long> ids);


    /**
     * 新冠防控列表
     * @param customerId
     * @param category
     * @return
     */
    List<CustomerCovid19> listCustomerCovid19(@Param("customerId") Long customerId,
                                              @Param("category") Integer category);
}
