package com.senox.user.mapper;

import com.senox.user.domain.Customer;
import com.senox.user.vo.CustomerSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/31 8:05
 */
@Mapper
@Repository
public interface CustomerMapper {

    /**
     * 添加客户
     * @param customer
     * @return
     */
    int addCustomer(Customer customer);

    /**
     * 更新客户
     * @param customer
     * @return
     */
    int updateCustomer(Customer customer);

    /**
     * 根据id查找客户
     * @param id
     * @return
     */
    Customer findById(Long id);

    /**
     * 根据身份证查找客户
     * @param idcard
     * @return
     */
    Customer findByIdcard(String idcard);

    /**
     * 根据客户编号查找客户
     * @param serialNo
     * @return
     */
    Customer findBySerialNo(String serialNo);

    /**
     * 获取最大的编号
     * @return
     */
    String findMaxSerialNo();

    /**
     * 客户统计
     * @param searchVo
     * @return
     */
    int countCustomer(CustomerSearchVo searchVo);

    /**
     * 客户列表
     * @param searchVo
     * @return
     */
    List<Customer> listCustomer(CustomerSearchVo searchVo);
}
