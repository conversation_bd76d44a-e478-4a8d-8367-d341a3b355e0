package com.senox.user.mapper;

import com.senox.user.domain.Department;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/15 10:39
 */
@Mapper
@Repository
public interface DepartmentMapper {

    /**
     * 添加部门
     * @param department
     * @return
     */
    int addDepartment(Department department);

    /**
     * 更新部门
     * @param department
     * @return
     */
    int updateDepartment(Department department);

    /**
     * 根据id查找部门
     * @param id
     * @return
     */
    Department findById(Long id);

    /**
     * 根据名字查找部门
     * @param parentId
     * @param name
     * @return
     */
    Department findByName(@Param("parentId") Long parentId, @Param("name") String name);

    /**
     * 部门列表
     * @param parentId
     * @return
     */
    List<Department> listByParentId(Long parentId);

    /**
     * 部门列表
     * @return
     */
    List<Department> listAll();
}
