package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.DiningInformation;
import com.senox.user.vo.DiningInformationSearchVo;
import com.senox.user.vo.DiningInformationVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface DiningInformationMapper extends BaseMapper<DiningInformation> {


    /**
     * 报餐订单数量
     * @param searchVo
     * @return
     */
    int count(DiningInformationSearchVo searchVo);


    /**
     * 报餐列表
     * @param searchVo
     * @return
     */
    List<DiningInformationVo> list(DiningInformationSearchVo searchVo);


    /**
     * 查询报餐订单
     * @param diningInformationList
     * @return
     */
    List<DiningInformation> diningInformationList(@Param("list") List<DiningInformation> diningInformationList);

    /**
     * 根据id查询报餐信息
     * @param id
     * @return
     */
    DiningInformationVo findById(Long id);

}
