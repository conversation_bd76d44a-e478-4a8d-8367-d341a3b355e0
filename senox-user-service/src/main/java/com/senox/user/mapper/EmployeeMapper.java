package com.senox.user.mapper;

import com.senox.user.domain.Employee;
import com.senox.user.vo.EmployeeSearchVo;
import com.senox.user.vo.EmployeeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 11:34
 */
@Mapper
@Repository
public interface EmployeeMapper {

    /**
     * 新增员工
     * @param employee
     * @return
     */
    int addEmployee(Employee employee);

    /**
     * 更新员工
     * @param employee
     * @return
     */
    int updateEmployee(Employee employee);

    /**
     * 根据id查找员工信息
     * @param id
     * @return
     */
    EmployeeVo findById(Long id);

    /**
     * 根据名称获取用户
     * @param employeeName
     * @return
     */
    Employee findByName(@Param("employeeName") String employeeName);

    /**
     * 查找代理企业的员工
     * @param delegateCompany
     * @return
     */
    Employee findDelegateCompanyEmployee(Long delegateCompany);

    /**
     * 统计员工
     * @param searchVo
     * @return
     */
    int countEmployee(EmployeeSearchVo searchVo);

    /**
     * 员工列表
     * @param searchVo
     * @return
     */
    List<EmployeeVo> listEmployee(EmployeeSearchVo searchVo);

    /**
     * 加载尚未报餐的员工列表
     * @param mealDate
     * @param excludeCompanies
     * @param offset
     * @param rows
     * @return
     */
    List<Employee> listNoBookedEmployee(@Param("mealDate") LocalDate mealDate,
                                        @Param("excludeCompanies") List<String> excludeCompanies,
                                        @Param("offset") int offset,
                                        @Param("rows") int rows);

}
