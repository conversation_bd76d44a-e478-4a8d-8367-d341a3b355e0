package com.senox.user.mapper;

import com.senox.user.domain.Company;
import com.senox.user.domain.Employee;
import com.senox.user.domain.EmployeeMealDelegate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 16:50
 */
@Mapper
@Repository
public interface EmployeeMealDelegateMapper {

    /**
     * 批量添加报餐代理
     * @param delegateList
     * @return
     */
    int batchAddDelegate(@Param("delegateList") List<EmployeeMealDelegate> delegateList);

    /**
     * 批量删除代理关系
     * @param ids
     * @return
     */
    int batchDeleteDelegator(@Param("ids") List<Long> ids);

    /**
     * 代理的员工关系列表
     * @param employeeId
     * @return
     */
    List<EmployeeMealDelegate> listByEmployee(Long employeeId);

    /**
     * 代理的员工列表
     * @param employeeId
     * @return
     */
    List<Employee> listDelegateEmployee(Long employeeId);

    /**
     * 代理公司
     * @param employeeId
     * @return
     */
    Company findDelegateCompany(Long employeeId);

}
