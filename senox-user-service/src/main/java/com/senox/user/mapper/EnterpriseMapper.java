package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.Enterprise;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/9 11:05
 */
@Mapper
@Repository
public interface EnterpriseMapper extends BaseMapper<Enterprise> {

    /**
     * 删除企业
     * @param enterprise
     */
    void deleteEnterprise(Enterprise enterprise);

    /**
     * 企业合计
     * @param search
     * @return
     */
    int countEnterprise(EnterpriseSearchVo search);

    /**
     * 企业列表
     * @param search
     * @return
     */
    List<EnterpriseViewVo> listEnterprise(EnterpriseSearchVo search);
}
