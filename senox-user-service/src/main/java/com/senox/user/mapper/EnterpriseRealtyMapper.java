package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.EnterpriseRealty;
import com.senox.user.vo.EnterpriseRealtyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/9 11:06
 */
@Mapper
@Repository
public interface EnterpriseRealtyMapper extends BaseMapper<EnterpriseRealty> {

    /**
     * 根据物业编号重置别名
     * @param realtySerials
     * @return
     */
    int resetAliasByRealtySerial(@Param("realtySerials") List<String> realtySerials);

    /**
     * 根据企业id获取物业关系
     * @param enterpriseIds
     * @return
     */
    List<EnterpriseRealtyVo> listVoByEnterpriseIds(@Param("enterpriseIds") List<Long> enterpriseIds);

    /**
     * 合租但仅有1个经营户的物业列表
     * @return
     */
    List<String> listUniqueEnterpriseRealtyWithAlias();
}
