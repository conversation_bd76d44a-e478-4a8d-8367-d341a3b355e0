package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.FeedBack;
import com.senox.user.vo.FeedBackSearchVo;
import com.senox.user.vo.FeedBackVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/31 16:45
 */
@Mapper
public interface FeedBackMapper extends BaseMapper<FeedBack> {

    /**
     * 建议合计
     * @param search
     * @return
     */
    int countFeedBack(FeedBackSearchVo search);

    /**
     * 建议列表
     * @param search
     * @return
     */
    List<FeedBackVo> listFeedBack(FeedBackSearchVo search);

    /**
     * 查看建议及包含的回复信息
     * @param id
     * @param isDetail
     * @return
     */
    FeedBackVo getFeedBackResultById(Long id,Boolean isDetail);
}
