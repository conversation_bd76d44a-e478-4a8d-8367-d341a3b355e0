package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.FeedBackReply;
import com.senox.user.vo.FeedBackReplyVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2023/6/1 9:40
 */
@Mapper
public interface FeedBackReplyMapper extends BaseMapper<FeedBackReply> {

    /**
     * 获取建议回复
     *
     * @param id
     * @return
     */
    FeedBackReplyVo findFeedBackReplyById(Long id);

}
