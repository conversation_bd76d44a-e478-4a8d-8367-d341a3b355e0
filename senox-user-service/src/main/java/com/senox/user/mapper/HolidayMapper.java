package com.senox.user.mapper;

import com.senox.user.domain.Holiday;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/19 8:42
 */
@Mapper
@Repository
public interface HolidayMapper {

    /**
     * 批量添加假期
     * @param list
     * @return
     */
    int batchAddHoliday(@Param("list") List<Holiday> list);

    /**
     * 批量更新假期
     * @param list
     * @return
     */
    int batchUpdateHoliday(@Param("list") List<Holiday> list);

    /**
     * 批量删除假期
     * @param dateList
     * @return
     */
    int batchDeleteHoliday(@Param("list") List<LocalDate> dateList);

    /**
     * 查找日期内假期列表
     * @param dateList
     * @return
     */
    List<Holiday> listHolidayInDate(@Param("dateList") List<LocalDate> dateList);

    /**
     * 查找日期范围内的假期
     * @param startDate
     * @param endDate
     * @return
     */
    List<Holiday> listHolidayInRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
