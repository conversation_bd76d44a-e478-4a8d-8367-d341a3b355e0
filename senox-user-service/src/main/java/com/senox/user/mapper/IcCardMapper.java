package com.senox.user.mapper;

import com.senox.user.vo.IcCardSearchVo;
import com.senox.user.vo.IcCardVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/15 16:51
 */
@Mapper
@Repository
public interface IcCardMapper {

    /**
     * IC卡统计
     * @param searchVo
     * @return
     */
    int countIcCard(IcCardSearchVo searchVo);

    /**
     * IC卡列表
     * @param searchVo
     * @return
     */
    List<IcCardVo> listIcCard(IcCardSearchVo searchVo);

}
