package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.MerchantAuthApply;
import com.senox.user.vo.MerchantAuthApplyListVo;
import com.senox.user.vo.MerchantAuthApplySearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Mapper
@Repository
public interface MerchantAuthApplyMapper extends BaseMapper<MerchantAuthApply> {


    /**
     * 权限列表总数
     * @param search 查询参数
     * @return 返回列表总数
     */
    int countApply(MerchantAuthApplySearchVo search);

    /**
     * 权限申请列表
     *
     * @param searchVo 查询参数
     * @return 返回列表
     */
    List<MerchantAuthApplyListVo> listApply(MerchantAuthApplySearchVo searchVo);

    /**
     * 待审核数量
     * @return
     */
    Integer auditCount();

}
