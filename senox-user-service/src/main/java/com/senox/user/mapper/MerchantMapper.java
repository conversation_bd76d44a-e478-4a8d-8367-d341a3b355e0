package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.Merchant;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Mapper
@Repository
public interface MerchantMapper extends BaseMapper<Merchant> {

    /**
     * 商户合计
     * @param search
     * @return
     */
    int countMerchant(MerchantSearchVo search);

    /**
     * 商户列表
     * @param search
     * @return
     */
    List<Merchant> listMerchant(MerchantSearchVo search);

    /**
     * 获取客户信息列表
     * @param search
     * @return
     */
    List<MerchantVo> listMerchantView(MerchantSearchVo search);

    /**
     * 批量更新收费标准
     *
     * @param ids       id集
     * @param chargesId 收费标准id
     * @param fullData  true:全量;false:增量
     */
    void updateChargesBatch(Collection<Long> ids, Long chargesId, Boolean fullData);
}
