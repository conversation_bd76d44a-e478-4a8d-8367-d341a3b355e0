package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.PrizeDrawNumber;
import com.senox.user.vo.PrizeDrawNumberVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/4/15 11:28
 */
@Mapper
public interface PrizeDrawNumberMapper extends BaseMapper<PrizeDrawNumber> {

    /**
     * 抽奖次数总数
     * @param drawNumberVo
     * @return
     */
    int countPrizeDrawNumber(PrizeDrawNumberVo drawNumberVo);
}
