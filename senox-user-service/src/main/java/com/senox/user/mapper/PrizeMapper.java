package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.Prize;
import com.senox.user.vo.PrizeSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:00
 */
@Mapper
public interface PrizeMapper extends BaseMapper<Prize> {

    /**
     * 奖品总数
     * @param searchVo
     * @return
     */
    int countPrize(PrizeSearchVo searchVo);

    /**
     * 奖品列表
     * @param searchVo
     * @return
     */
    List<Prize> listPrize(PrizeSearchVo searchVo);

    /**
     * 扣减奖品数量
     * @param id
     * @return
     */
    int reduceRemainingNum(@Param("id") Long id);
}
