package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.PrizeRecords;
import com.senox.user.vo.PrizeRecordsSearchVo;
import com.senox.user.vo.PrizeRecordsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/11 11:16
 */
@Mapper
public interface PrizeRecordsMapper extends BaseMapper<PrizeRecords> {

    /**
     * 抽奖记录总数
     * @param searchVo
     * @return
     */
    int countRecords(PrizeRecordsSearchVo searchVo);

    /**
     * 抽奖记录分页
     * @param searchVo
     * @return
     */
    List<PrizeRecordsVo> listRecords(PrizeRecordsSearchVo searchVo);

    /**
     * 根据id查询获奖记录
     * @param id
     * @return
     */
    PrizeRecordsVo findPrizeRecordsVoById(@Param("id") Long id);

    /**
     * 根据uuid查询获奖记录
     * @param uuid
     * @return
     */
    PrizeRecordsVo findPrizeRecordsVoByUuid(@Param("uuid") String uuid);
}
