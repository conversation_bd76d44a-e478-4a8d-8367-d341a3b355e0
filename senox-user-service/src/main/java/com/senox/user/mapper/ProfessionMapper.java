package com.senox.user.mapper;

import com.senox.user.domain.Profession;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/31 15:01
 */
@Mapper
@Repository
public interface ProfessionMapper {

    /**
     * 添加行业
     * @param profession
     * @return
     */
    int addProfession(Profession profession);

    /**
     * 更新行业
     * @param profession
     * @return
     */
    int updateProfession(Profession profession);

    /**
     * 根据id查找行业
     * @param id
     * @return
     */
    Profession findById(Long id);

    /**
     * 根据名字查找id
     * @param name
     * @return
     */
    Long findIdByName(String name);

    /**
     * 行业列表
     * @return
     */
    List<Profession> listAll();
}
