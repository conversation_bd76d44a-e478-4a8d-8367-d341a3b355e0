package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.ReservationRecord;
import com.senox.user.vo.ReservationRecordSearchVo;
import com.senox.user.vo.ReservationRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:07
 */
@Mapper
public interface ReservationRecordMapper extends BaseMapper<ReservationRecord> {

    /**
     *
     * @param id
     * @return
     */
    ReservationRecordVo findInfoById(@Param("id") Long id);

    /**
     * 预约记录统计
     * @param searchVo
     * @return
     */
    int countReservationRecord(ReservationRecordSearchVo searchVo);

    /**
     * 预约记录列表
     * @param searchVo
     * @return
     */
    List<ReservationRecordVo> listReservationRecord(ReservationRecordSearchVo searchVo);

    /**
     * 预约记录随行人总数
     * @param searchVo
     * @return
     */
    ReservationRecordVo sumReservationRecord(ReservationRecordSearchVo searchVo);
}
