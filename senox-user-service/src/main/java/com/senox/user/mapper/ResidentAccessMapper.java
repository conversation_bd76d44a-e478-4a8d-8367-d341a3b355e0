package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.ResidentAccess;
import com.senox.user.vo.ResidentAccessResultVo;
import com.senox.user.vo.ResidentAccessVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/5/22 15:42
 */
@Mapper
public interface ResidentAccessMapper extends BaseMapper<ResidentAccess> {

    /**
     * 根据住户编号查询住户的权限
     *
     * @param residentNo
     * @param isDetail
     * @return
     */
    ResidentAccessResultVo residentAccessResultByNo(String residentNo, Boolean isDetail);

    /**
     * 根据设备Id查询所拥有的权限列表
     * @param deviceId
     * @return
     */
    List<ResidentAccessVo> listResidentAccessByDeviceId(@Param("deviceId") Long deviceId);
}
