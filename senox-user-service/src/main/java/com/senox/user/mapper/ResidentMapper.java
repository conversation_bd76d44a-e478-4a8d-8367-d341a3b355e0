package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.Resident;
import com.senox.user.vo.ResidentSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 8:09
 */
@Mapper
public interface ResidentMapper extends BaseMapper<Resident> {

    /**
     * 获取最大的住户单号
     *
     * @param prefix
     * @return
     */
    String findMaxResidentNo(String prefix);

    /**
     * 住户count
     * @param search
     * @return
     */
    int count(ResidentSearchVo search);

    /**
     * 住户列表
     * @param search
     * @return
     */
    List<Resident> list(ResidentSearchVo search);
}
