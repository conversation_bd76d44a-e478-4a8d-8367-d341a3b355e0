package com.senox.user.mapper;

import com.senox.user.domain.Role;
import com.senox.user.domain.RoleCos;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/26 14:26
 */
@Mapper
@Repository
public interface RoleMapper {

    /**
     * 添加角色
     * @param role
     * @return
     */
    int addRole(Role role);

    /**
     * 更新角色
     * @param role
     * @return
     */
    int updateRole(Role role);

    /**
     * 删除角色
     * @param id
     * @return
     */
    int deleteRole(Long id);

    /**
     * 根据id查找角色
     * @param id
     * @return
     */
    Role findById(Long id);

    /**
     * 根据角色名查找角色id
     * @param name
     * @return
     */
    Long findIdByName(String name);

    /**
     * 角色列表
     * @return
     */
    List<Role> listAll();

    /**
     * 批量添加角色权限列表
     * @param roleCosList
     * @return
     */
    int batchAddRoleCos(@Param("roleCosList") List<RoleCos> roleCosList);

    /**
     * 删除角色权限
     * @param roleId
     * @return
     */
    int deleteRoleCos(Long roleId);

    /**
     * 批量删除角色权限
     * @param roleId
     * @param cosIds
     * @return
     */
    int batchDelRoleCos(@Param("roleId") Long roleId, @Param("cosIds") List<Long> cosIds);

    /**
     * 判断权限占用情况
     * @param cosId
     * @return
     */
    int countRoleCos(Long cosId);

    /**
     * 角色权限关系表
     * @param roleId
     * @return
     */
    List<Long> listRoleCos(Long roleId);

}
