package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.VoteCategory;
import com.senox.user.vo.VoteCategorySearchVo;
import com.senox.user.vo.VoteCategoryVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 10:05
 */
@Mapper
public interface VoteCategoryMapper extends BaseMapper<VoteCategory> {

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int count(VoteCategorySearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<VoteCategoryVo> list(VoteCategorySearchVo searchVo);
}
