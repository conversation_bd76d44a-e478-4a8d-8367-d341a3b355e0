package com.senox.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.user.domain.VoteResources;
import com.senox.user.vo.VoteResourcesSearchVo;
import com.senox.user.vo.VoteResourcesVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:45
 */
@Mapper
public interface VoteResourcesMapper extends BaseMapper<VoteResources> {

    /**
     * 获取最大编号
     * @param activityId
     * @return
     */
    Integer findMaxSerial(Long activityId);

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int count(VoteResourcesSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<VoteResourcesVo> list(VoteResourcesSearchVo searchVo);
}
