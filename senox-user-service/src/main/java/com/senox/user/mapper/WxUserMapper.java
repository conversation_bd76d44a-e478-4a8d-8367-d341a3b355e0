package com.senox.user.mapper;

import com.senox.user.vo.WxUserRealtyVo;
import com.senox.user.vo.WxUserRemarkVo;
import com.senox.user.vo.WxUserSearchVo;
import com.senox.user.vo.WxUserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/3 16:02
 */
@Mapper
@Repository
public interface WxUserMapper {

    /**
     * 更新微信用户
     * @param user
     * @return
     */
    int updateWxUser(WxUserVo user);

    /**
     * 根据id查找用户
     * @param id
     * @return
     */
    WxUserVo findById(Long id);

    /**
     * 根据openid查找用户
     * @param appId
     * @param openid
     * @return
     */
    WxUserVo findByOpenid(@Param("appId") String appId, @Param("openid") String openid);

    /**
     * 微信用户统计
     * @param search
     * @return
     */
    int countWxUser(WxUserSearchVo search);

    /**
     * 微信用户列表
     * @param search
     * @return
     */
    List<WxUserVo> listWxUser(WxUserSearchVo search);

    /**
     * 删除微信用户绑定记录
     * @param userId
     * @param realtyId
     * @return
     */
    int deleteWxUserRealty(@Param("userId") Long userId, @Param("realtyId") Long realtyId);

    /**
     * 查找用户绑定记录
     * @param userId
     * @param realtyId
     * @return
     */
    WxUserRealtyVo findWxUserRealty(@Param("userId") Long userId, @Param("realtyId") Long realtyId);

    /**
     * 微信用户绑定物业列表
     * @param userId
     * @return
     */
    List<WxUserRealtyVo> listWxUserRealty(Long userId);

    /**
     * 绑定微信用户物业
     * @param userRealty
     * @return
     */
    int addWxUserRealty(WxUserRealtyVo userRealty);

    /**
     * 添加解绑历史
     * @param userRealty
     * @return
     */
    int addUnbindHistory(WxUserRealtyVo userRealty);

    /**
     * 更新微信账号备注
     * @param remarkVo
     * @return
     */
    int updateWxUserRemark(WxUserRemarkVo remarkVo);

    /**
     * 解绑骑手
     * @param openid
     * @return
     */
    int unbindRider(@Param("openid") String openid);

    /**
     * 根据绑定的骑手id查找用户
     * @param riderId
     * @return
     */
    WxUserVo findByRiderId(@Param("riderId") Long riderId);
}
