package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.config.VoteActivityConfig;
import com.senox.user.constant.ActivityCategory;
import com.senox.user.constant.UserConst;
import com.senox.user.constant.ActivityStatus;
import com.senox.user.domain.Activity;
import com.senox.user.mapper.ActivityMapper;
import com.senox.user.mapper.PrizeRecordsMapper;
import com.senox.user.utils.ContextUtils;
import com.senox.user.vo.ActivitySearchVo;
import com.senox.user.vo.ActivityVo;
import com.senox.user.vo.PrizeRecordsSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/16 8:23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ActivityService extends ServiceImpl<ActivityMapper, Activity> {

    private final VoteActivityConfig activityConfig;
    private final PrizeRecordsMapper prizeRecordsMapper;

    /**
     * 添加活动
     * @param activity
     * @return
     */
    public Long addActivity(Activity activity) {
        ContextUtils.initEntityCreator(activity);
        activity.setCreateTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(activity);
        activity.setModifiedTime(LocalDateTime.now());
        boolean save = save(activity);
        if (save) {
            //活动校验
            activityCheck(activity);
        }
        return save ? activity.getId() : 0L;
    }

    /**
     * 更新活动
     * @param activity
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateActivity(Activity activity) {
        if (!WrapperClassUtils.biggerThanLong(activity.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        Activity dbActivity = findById(activity.getId());
        if (null == dbActivity) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (activity.getStatus().equals(ActivityStatus.UN_EFFECTIVE.getNumber())) {
            activity.setStopTime(LocalDateTime.now());
        }
        if (activity.getStatus().equals(ActivityStatus.EFFECTIVE.getNumber())) {
            activity.setStopTime(null);
        }
        ContextUtils.initEntityModifier(activity);
        activity.setModifiedTime(LocalDateTime.now());
        updateById(activity);
        //活动校验
        activityCheck(activity);
    }

    /**
     * 生成活动链接
     * @param id
     * @return 链接
     */
    public String generateActivityUrl(Long id) {
        Activity activity = findById(id);
        if (activity == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到该活动");
        }
        if (!StringUtils.isBlank(activity.getUuid())) {
            throw new BusinessException("请勿重复生产链接！");
        }
        activity.setUuid(StringUtils.randStr(8));
        Map<String, String> urlMap = activityConfig.getUrl();
        String url = urlMap.get(ActivityCategory.fromNumber(activity.getCategory()).name());
        if (StringUtils.isBlank(url)) {
            throw new BusinessException("未配置链接地址");
        }
        activity.setUrl(url.concat(activity.getUuid()));
        ContextUtils.initEntityModifier(activity);
        activity.setModifiedTime(LocalDateTime.now());
        return updateById(activity) ? activity.getUrl() : StringUtils.EMPTY;
    }

    /**
     * 根据id获取活动
     * @param id
     * @return
     */
    public Activity findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据uuid获取活动
     * @param uuid
     * @return
     */
    public Activity findByUuid(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return null;
        }
        return getOne(new QueryWrapper<Activity>().lambda().eq(Activity::getUuid, uuid));
    }

    /**
     * 根据id删除活动
     * @param id
     */
    public void deleteById(Long id) {
        Activity dbActivity = findById(id);
        if (dbActivity == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到该活动");
        }
        dbActivity.setStatus(ActivityStatus.UN_EFFECTIVE.getNumber());
        dbActivity.setDisabled(Boolean.TRUE);
        dbActivity.setName(dbActivity.getName().concat("-").concat(dbActivity.getId().toString()));
        updateById(dbActivity);
    }

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    public int count(ActivitySearchVo searchVo) {
        return getBaseMapper().count(searchVo);
    }

    /**
     * 列表
     * @param searchVo
     * @return
     */
    public List<ActivityVo> list(ActivitySearchVo searchVo) {
        return getBaseMapper().list(searchVo);
    }

    /**
     * 活动分页
     * @param searchVo
     * @return
     */
    public PageResult<ActivityVo> page(ActivitySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().count(searchVo), () -> getBaseMapper().list(searchVo));
    }

    /**
     * 校验活动有效性
     * @param activity
     */
    public void checkStatus(Activity activity) {
        if (activity == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "活动不存在");
        }
        if (LocalDateTime.now().isAfter(activity.getEndTime())
                || LocalDateTime.now().isBefore(activity.getStartTime())) {
            throw new BusinessException("不在活动时间范围内！");
        }
    }

    /**
     * 活动校验
     * @param activity
     */
    private void activityCheck(Activity activity) {
        if (ActivityCategory.fromNumber(activity.getCategory()) == ActivityCategory.VOTE) {
            //重置可以票数
            resetLimitNumber(activity.getId());
        }
        if (ActivityCategory.fromNumber(activity.getCategory()) == ActivityCategory.PRIZE_DRAW) {
            PrizeRecordsSearchVo searchVo = new PrizeRecordsSearchVo();
            searchVo.setActivityId(activity.getId());
            int countRecords = prizeRecordsMapper.countRecords(searchVo);
            if (activity.getLimitNum() <= countRecords) {
                throw new BusinessException("参与抽奖人数不能小于已抽奖人数！");
            }
        }
    }

    /**
     * 重置活动可用票数
     * @param id
     */
    private void resetLimitNumber(Long id) {
        Activity activity = findById(id);
        if (activity == null) {
            throw new BusinessException("活动未存在！");
        }
        String key = String.format(UserConst.Cache.VOTE_LIMIT_NUMBERS, id);
        RedisUtils.set(key, activity.getLimitNum());
    }

    /**
     * 活动限制投票次数
     * @param id
     * @return
     */
    public Integer limitNumber(Long id) {
        String key = String.format(UserConst.Cache.VOTE_LIMIT_NUMBERS, id);
        Integer val = RedisUtils.get(key);
        if (val == null) {
            //重置活动可用票数
            resetLimitNumber((id));
            return RedisUtils.get(key);
        }
        return val;
    }
}
