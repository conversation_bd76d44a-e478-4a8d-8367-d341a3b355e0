package com.senox.user.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.constant.AreaCategory;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Area;
import com.senox.user.mapper.AreaMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/15 8:56
 */
@Service
public class AreaService {

    private static final Logger logger = LoggerFactory.getLogger(AreaService.class);

    @Autowired
    private AreaMapper areaMapper;

    /**
     * 添加地区信息
     * @param area
     * @return
     */
    public Long addArea(Area area) {
        prepareArea(area);
        // 校验编码唯一性
        if (checkAreaExists(null, area.getSerialNo())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "重复的地区编号");
        }

        int result = areaMapper.addArea(area);
        long resultL = result > 0 ? area.getId() : 0L;

        // reset cache
        if (resultL > 0L) {
            String cacheKey = String.format(UserConst.Cache.KEY_AREA, area.getParentId());
            RedisUtils.del(cacheKey);
        }
        return resultL;
    }

    /**
     * 更新地区信息
     * @param area
     * @return
     */
    public boolean updateArea(Area area) {
        if (!WrapperClassUtils.biggerThanLong(area.getId(), 0L)) {
            return false;
        }
        // 校验编码唯一性
        if (!StringUtils.isBlank(area.getSerialNo()) && checkAreaExists(area.getId(), area.getSerialNo())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "重复的地区编号");
        }

        // 查找原数据
        Area dbArea = areaMapper.findById(area.getId());
        if (dbArea == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }

        boolean result =  areaMapper.updateArea(area) > 0;
        // reset cache
        if (result) {
            List<String> cacheKeys = new ArrayList<>(2);
            cacheKeys.add(String.format(UserConst.Cache.KEY_AREA, dbArea.getParentId()));
            // 更新了父级列表，并且非禁用
            if (!WrapperClassUtils.isTrue(area.getDisabled())
                    && !Objects.equals(dbArea.getParentId(), area.getParentId())) {
                cacheKeys.add(String.format(UserConst.Cache.KEY_AREA, area.getParentId()));
            }
            RedisUtils.del(cacheKeys.toArray(new String[0]));
        }
        return result;
    }

    /**
     * 根据id查找地区
     * @param id
     * @return
     */
    public Area findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return areaMapper.findById(id);
    }

    /**
     * 查找子地区列表
     * @param parentId
     * @param categories
     * @return
     */
    public List<Area> listByParentIdAndCategory(Long parentId, List<AreaCategory> categories) {
        parentId = parentId == null ? 0L : parentId;
        List<Area> resultList = null;

        // load from cache
        String cacheKey = String.format(UserConst.Cache.KEY_AREA, parentId);
        String cacheVal = RedisUtils.get(cacheKey);
        if (!StringUtils.isBlank(cacheVal)) {
            logger.debug("Load area from cache {}", cacheKey);
            resultList = JsonUtils.json2GenericObject(cacheVal, new TypeReference<List<Area>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            logger.debug("Load area from db {}", cacheKey);
            List<Integer> categoryList = categories.stream().map(AreaCategory::getValue).collect(Collectors.toList());
            resultList = areaMapper.listByParentIdAndCategory(parentId, categoryList);

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(cacheKey, JsonUtils.object2Json(resultList), UserConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }

    /**
     * 区域列表
     * @return
     */
    public List<Area> listAll() {
        return areaMapper.listArea();
    }

    /**
     * 物业编号是否存在
     * @param id
     * @param serialNo
     * @return
     */
    private boolean checkAreaExists(Long id, String serialNo) {
        if (StringUtils.isBlank(serialNo)) {
            return false;
        }
        Long dbId = areaMapper.findIdBySerialNo(serialNo);
        if (dbId == null) {
            return false;
        }

        return id == null || id < 1L ? dbId > 0L : !Objects.equals(id, dbId);
    }


    private void prepareArea(Area area) {
        if (area.getName() == null) {
            area.setName(StringUtils.EMPTY);
        }
        if (area.getBriefName() == null) {
            area.setBriefName(StringUtils.EMPTY);
        }
        if (area.getCategory() == null) {
            area.setCategory(0);
        }
        if (area.getParentId() == null) {
            area.setParentId(0L);
        }
    }
}
