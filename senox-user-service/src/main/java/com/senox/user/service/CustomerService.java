package com.senox.user.service;

import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Customer;
import com.senox.user.domain.CustomerCovid19;
import com.senox.user.domain.CustomerExt;
import com.senox.user.mapper.CustomerCovid19Mapper;
import com.senox.user.mapper.CustomerExtMapper;
import com.senox.user.mapper.CustomerMapper;
import com.senox.user.vo.CustomerSearchVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/31 9:24
 */
@Service
public class CustomerService {

    private static final int EXPORT_PAGE_SIZE = 20000;

    @Value("${senox.customer.serial.length}")
    private Integer serialLength;
    @Value("${senox.customer.serial.fillChar}")
    private Character fillChar;

    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private CustomerExtMapper customerExtMapper;
    @Autowired
    private CustomerCovid19Mapper customerCovid19Mapper;

    /**
     * 添加客户
     * @param customer
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addCustomer(Customer customer, CustomerExt ext, List<CustomerCovid19> covid19List) {
        // 客户信息
        prepareCustomer(customer);
        /*
        if (checkCustomerExist(customer)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "已存在相同身份证的用户");
        }
        */
        int result = customerMapper.addCustomer(customer);

        // 扩展信息
        if (result > 0) {
            // 客户扩展信息
            ext = prepareCustomerExt(customer, ext);
            customerExtMapper.addCustomerExt(ext);

            // 新冠防控记录
            if (!CollectionUtils.isEmpty(covid19List)) {
                covid19List.forEach(x -> {
                    x.setCustomerId(customer.getId());
                    x.setCreatorId(customer.getCreatorId());
                    x.setCreatorName(customer.getCreatorName());
                    x.setModifierId(customer.getModifierId());
                    x.setModifierName(customer.getModifierName());
                });
                customerCovid19Mapper.batchAddCustomerCovid19(covid19List);
            }

        }
        return customer.getId();
    }

    /**
     * 更新客户
     * @param customer
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCustomer(Customer customer, CustomerExt ext, List<CustomerCovid19> covid19List) {
        if (!WrapperClassUtils.biggerThanLong(customer.getId(), 0L)) {
            return false;
        }
        /*
        if (checkCustomerExist(customer)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "已存在相同身份证的用户");
        }
        */
        boolean result = customerMapper.updateCustomer(customer) > 0;

        if (result) {
            // 客户扩展信息
            if (ext != null) {
                ext.setCustomerId(customer.getId());
                ext.setModifierId(customer.getModifierId());
                ext.setModifierName(customer.getModifierName());
                customerExtMapper.updateCustomerExt(ext);
            }

            // 客户新冠防控信息
            if (covid19List != null) {
                if (!CollectionUtils.isEmpty(covid19List)) {
                    covid19List.forEach(x -> {
                        x.setCustomerId(customer.getId());
                        x.setCreatorId(customer.getModifierId());
                        x.setCreatorName(customer.getModifierName());
                        x.setModifierId(customer.getModifierId());
                        x.setModifierName(customer.getModifierName());
                    });
                    // 与原记录比较
                    List<CustomerCovid19> srcList = customerCovid19Mapper.listCustomerCovid19(customer.getId(), null);
                    DataSepDto<CustomerCovid19> covid19SepData = sepCustomerCovid(srcList, covid19List);

                    // 保存客户新冠防控信息
                    if (!CollectionUtils.isEmpty(covid19SepData.getAddList())) {
                        customerCovid19Mapper.batchAddCustomerCovid19(covid19SepData.getAddList());
                    }
                    if (!CollectionUtils.isEmpty(covid19SepData.getRemoveList())) {
                        List<Long> ids = covid19SepData.getRemoveList().stream().map(CustomerCovid19::getId).collect(Collectors.toList());
                        customerCovid19Mapper.deleteCustomerCovid19(customer.getId(), ids);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 根据id查找客户
     * @param id
     * @return
     */
    public Customer findById(Long id) {
        return !WrapperClassUtils.biggerThanLong(id, 0L) ? null : customerMapper.findById(id);
    }

    /**
     * 根据身份证查找客户
     * @param idcard
     * @return
     */
    public Customer findByIdcard(String idcard) {
        return StringUtils.isBlank(idcard) ? null : customerMapper.findByIdcard(idcard);
    }

    /**
     * 根据客户编号查找客户
     * @param serialNo
     * @return
     */
    public Customer findBySerialNo(String serialNo) {
        return StringUtils.isBlank(serialNo) ? null : customerMapper.findBySerialNo(serialNo);
    }

    /**
     * 查找客户最大序号
     * @return
     */
    public String findMaxSerial() {
        return customerMapper.findMaxSerialNo();
    }

    /**
     * 客户分页
     * @param searchVo
     * @return
     */
    public PageResult<Customer> listCustomerPage(CustomerSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = customerMapper.countCustomer(searchVo);
        searchVo.prepare();

        if (totalSize <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<Customer> resultList = customerMapper.listCustomer(searchVo);
        return PageUtils.resultPage(searchVo, totalSize, resultList);
    }

    /**
     * 客户列表（无分页）
     * @param searchVo
     * @return
     */
    public List<Customer> listCustomerWithoutPage(CustomerSearchVo searchVo) {
        searchVo.setPageNo(1);
        searchVo.setPageSize(EXPORT_PAGE_SIZE);
        searchVo.prepare();
        return customerMapper.listCustomer(searchVo);
    }

    /**
     * 根据id查找客户扩展信息
     * @param customerId
     * @return
     */
    public CustomerExt findExtByCustomerId(Long customerId) {
        return !WrapperClassUtils.biggerThanLong(customerId, 0L) ? null : customerExtMapper.findByCustomerId(customerId);
    }

    /**
     * 客户新冠防控记录
     * @param customerId
     * @return
     */
    public List<CustomerCovid19> listCustomerCovid19(Long customerId) {
        if (!WrapperClassUtils.biggerThanLong(customerId, 0L)) {
            return Collections.emptyList();
        }
        return customerCovid19Mapper.listCustomerCovid19(customerId, null);
    }

    /**
     * 初始化客户信息
     * @param customer
     */
    private void prepareCustomer(Customer customer) {
        if (customer.getIdcard() == null) {
            customer.setIdcard(StringUtils.EMPTY);
        }
        if (customer.getIdcardType() == null) {
            customer.setIdcardType(0);
        }
        if (customer.getGender() == null) {
            customer.setGender(0);
        }
        if (customer.getNation() == null) {
            customer.setNation(StringUtils.EMPTY);
        }
        if (customer.getTelephone() == null) {
            customer.setTelephone(StringUtils.EMPTY);
        }
        if (customer.getProvinceId() == null) {
            customer.setProvinceId(0L);
        }
        if (customer.getCityId() == null) {
            customer.setCityId(0L);
        }
        if (customer.getWorkplaceRegionId() == null) {
            customer.setWorkplaceRegionId(0L);
        }
        if (customer.getWorkplaceStreetId() == null) {
            customer.setWorkplaceStreetId(0L);
        }
        if (customer.getProfessionId() == null) {
            customer.setProfessionId(0L);
        }
        if (customer.getNatTested() == null) {
            customer.setNatTested(Boolean.FALSE);
        }
        if (customer.getCovid19Vaccination() == null) {
            customer.setCovid19Vaccination(Boolean.FALSE);
        }
        // 客户编号
        if (StringUtils.isBlank(customer.getSerialNo())) {
            customer.setSerialNo(prepareCustomerSerialNo(serialLength, fillChar));
        }
        if (customer.getZnwSynced() == null) {
            customer.setZnwSynced(Boolean.FALSE);
        }
    }

    /**
     * 客户编号
     * @param length
     * @param fillChar
     * @return
     */
    private String prepareCustomerSerialNo(int length, char fillChar) {
        Long result = RedisUtils.incr(UserConst.Cache.KEY_CUSTOMER_SERIALNO);
        String dbMaxSerial = findMaxSerial();
        Long maxSerial = dbMaxSerial == null ? 0L : NumberUtils.parseLong(dbMaxSerial, 0L);
        if (result < maxSerial) {
            result = maxSerial + 1;
            RedisUtils.set(UserConst.Cache.KEY_CUSTOMER_SERIALNO, result);
        }
        return StringUtils.fixLength(String.valueOf(result), length, fillChar);
    }

    /**
     * 初始化客户扩展信息
     * @param customer
     * @param ext
     * @return
     */
    private CustomerExt prepareCustomerExt(Customer customer, CustomerExt ext) {
        if (ext == null) {
            ext = new CustomerExt();
        }
        ext.setCustomerId(customer.getId());
        ext.setCreatorId(customer.getModifierId());
        ext.setCreatorName(customer.getCreatorName());
        ext.setModifierId(customer.getModifierId());
        ext.setModifierName(customer.getModifierName());
        return ext;
    }

    private boolean checkCustomerExist(Customer customer) {
        if (StringUtils.isBlank(customer.getIdcard())) {
            return false;
        }
        Customer idcardCustomer = findByIdcard(customer.getIdcard());
        if (idcardCustomer == null) {
            return false;
        }
      return !WrapperClassUtils.biggerThanLong(customer.getId(), 0L) || !Objects.equals(customer.getId(), idcardCustomer.getId());
    }

    /**
     * 比较客户新冠防控记录，得出待修改记录
     * @param src
     * @param target
     * @return
     */
    private DataSepDto<CustomerCovid19> sepCustomerCovid(List<CustomerCovid19> src, List<CustomerCovid19> target) {
        DataSepDto<CustomerCovid19> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(target)) {
            // 目标记录为空，删掉所有原始记录
            result.setRemoveList(src);

        } else if (CollectionUtils.isEmpty(src)) {
            // 原始记录为空，新增所有目标记录
            result.setAddList(target);

        } else {
            // 新增所有在原始记录中没有的目标记录
            List<CustomerCovid19> addList = target.stream()
                    .filter(x -> !isCustomerCovidExist(src, x))
                    .collect(Collectors.toList());
            // 移除所有在目标记录中没有的原始记录
            List<CustomerCovid19> removeList = src.stream()
                    .filter(x -> !isCustomerCovidExist(target, x))
                    .collect(Collectors.toList());
            result.setAddList(addList);
            result.setRemoveList(removeList);
        }
        return result;
    }

    /**
     * 判断客户新冠防控记录是否在列表中存在
     * @param list
     * @param item
     * @return
     */
    private boolean isCustomerCovidExist(List<CustomerCovid19> list, CustomerCovid19 item) {
        return list.stream().anyMatch(x -> x.getCustomerId().equals(item.getCustomerId())
                && x.getOperateDate().compareTo(item.getOperateDate()) == 0
                && x.getCategory().equals(item.getCategory())
                && Objects.equals(x.getRemark(), item.getRemark()));
    }
}
