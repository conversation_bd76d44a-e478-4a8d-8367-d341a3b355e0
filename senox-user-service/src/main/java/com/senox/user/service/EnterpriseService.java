package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminUserDto;
import com.senox.user.domain.Enterprise;
import com.senox.user.mapper.EnterpriseMapper;
import com.senox.user.vo.EnterpriseRealtyVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/9 11:08
 */
@Service
@RequiredArgsConstructor
public class EnterpriseService extends ServiceImpl<EnterpriseMapper, Enterprise> {

    private final EnterpriseRealtyService enterpriseRealtyService;

    /**
     * 保存企业
     * @param enterprise
     * @param realtySerials
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveEnterprise(Enterprise enterprise, List<EnterpriseRealtyVo> realtySerials) {
        if (!StringUtils.isBlank(enterprise.getName()) && checkEnterpriseExists(enterprise)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "企业已存在");
        }

        // 保存企业信息
        if (!WrapperClassUtils.biggerThanLong(enterprise.getId(), 0L)) {
            addEnterprise(enterprise);
        } else {
            updateEnterprise(enterprise);
        }

        // 保存企业物业关系
        enterpriseRealtyService.saveEnterpriseRealty(enterprise.getId(), realtySerials,
                new AdminUserDto(enterprise.getModifierId(), enterprise.getModifierName()));
        return enterprise.getId();
    }

    /**
     * 添加企业
     * @param enterprise
     */
    private void addEnterprise(Enterprise enterprise) {
        if (enterprise.getFirefightingEmphasis() == null) {
            enterprise.setFirefightingEmphasis(Boolean.FALSE);
        }
        enterprise.setCreatorId(enterprise.getModifierId());
        enterprise.setCreatorName(enterprise.getModifierName());
        enterprise.setCreateTime(LocalDateTime.now());
        enterprise.setModifiedTime(LocalDateTime.now());

        save(enterprise);
    }

    /**
     * 更新企业
     * @param enterprise
     */
    private void updateEnterprise(Enterprise enterprise) {
        enterprise.setModifiedTime(LocalDateTime.now());
        updateById(enterprise);
    }

    /**
     * 删除企业
     * @param enterprise
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteEnterprise(Enterprise enterprise) {
        if (!WrapperClassUtils.biggerThanLong(enterprise.getId(), 0L)) {
            return;
        }

        // 删除企业物业关系
        enterpriseRealtyService.removeByEnterpriseId(enterprise.getId());
        // 删除企业
        getBaseMapper().deleteEnterprise(enterprise);
    }

    /**
     * 保存消防重点场所
     * @param enterpriseIds
     * @param enterprise
     */
    public void saveEnterpriseFirefightingEmphasis(List<Long> enterpriseIds, Enterprise enterprise) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return;
        }

        // 消防重点场所
        LambdaQueryWrapper<Enterprise> queryWrapper = new QueryWrapper<Enterprise>()
                .lambda()
                .in(Enterprise::getId, enterpriseIds)
                .eq(Enterprise::getFirefightingEmphasis, !BooleanUtils.isTrue(enterprise.getFirefightingEmphasis()));
        enterprise.setFirefightingEmphasis(BooleanUtils.isTrue(enterprise.getFirefightingEmphasis()));
        enterprise.setModifiedTime(LocalDateTime.now());
        update(enterprise, queryWrapper);
    }

    /**
     * 根据id朝朝企业
     * @param id
     * @return
     */
    public Enterprise findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 根据企业名查找企业
     * @param enterprise
     * @return
     */
    public Enterprise findByName(Enterprise enterprise) {
        if (StringUtils.isBlank(enterprise.getName()) && StringUtils.isBlank(enterprise.getFullName())) {
            return null;
        }

        Wrapper<Enterprise> queryWrapper = new QueryWrapper<Enterprise>().lambda()
                .eq(Enterprise::getName, enterprise.getName())
                .eq(Enterprise::getFullName, enterprise.getFullName());
        return getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 企业合计
     * @param search
     * @return
     */
    public int countEnterprise(EnterpriseSearchVo search) {
        return getBaseMapper().countEnterprise(search);
    }

    /**
     * 企业列表
     * @param search
     * @return
     */
    public List<EnterpriseViewVo> listEnterprise(EnterpriseSearchVo search) {
        return getBaseMapper().listEnterprise(search);
    }

    /**
     * 企业是否存在
     * @param enterprise
     * @return
     */
    private boolean checkEnterpriseExists(Enterprise enterprise) {
        Enterprise dbItem = findByName(enterprise);
        return dbItem != null && !Objects.equals(enterprise.getId(), dbItem.getId());
    }


}
