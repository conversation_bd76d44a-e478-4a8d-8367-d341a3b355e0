package com.senox.user.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Profession;
import com.senox.user.mapper.ProfessionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2020/12/31 15:32
 */
@Service
public class ProfessionService {

    @Autowired
    private ProfessionMapper professionMapper;

    /**
     * 添加行业
     * @param profession
     * @return
     */
    public Long addProfession(Profession profession) {
        if (checkProfessionExist(null, profession.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "行业已存在");
        }
        int result = professionMapper.addProfession(profession);
        long resultL = result < 1 ? 0L : profession.getId();

        if (resultL > 0) {
            RedisUtils.del(UserConst.Cache.KEY_PROFESSION);
        }
        return resultL;
    }

    /**
     * 更新行业
     * @param profession
     * @return
     */
    public boolean updateProfession(Profession profession) {
        if (!WrapperClassUtils.biggerThanLong(profession.getId(), 0)) {
            return false;
        }
        if (checkProfessionExist(profession.getId(), profession.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "行业已存在");
        }
        boolean result =  professionMapper.updateProfession(profession) > 0;

        // clear cache
        if (result) {
            RedisUtils.del(UserConst.Cache.KEY_PROFESSION);
        }
        return result;
    }

    /**
     * 获取行业
     * @param id
     * @return
     */
    public Profession getProfession(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return professionMapper.findById(id);
    }

    /**
     * 行业列表
     * @return
     */
    public List<Profession> listAll() {
        List<Profession> resultList = null;

        // load from cache
        String cacheValue = RedisUtils.get(UserConst.Cache.KEY_PROFESSION);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<Profession>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = professionMapper.listAll();

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(UserConst.Cache.KEY_PROFESSION, JsonUtils.object2Json(resultList), UserConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }


    /**
     * 判断行业是否存在
     * @param id
     * @param name
     * @return
     */
    private boolean checkProfessionExist(Long id, String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        Long dbId = professionMapper.findIdByName(name);
        if (!WrapperClassUtils.biggerThanLong(dbId, 0L)) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, dbId);
    }

}
