package com.senox.user.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.user.domain.Resident;
import com.senox.user.domain.ResidentAccess;
import com.senox.user.mapper.ResidentMapper;
import com.senox.user.vo.ResidentCheckVo;
import com.senox.user.vo.ResidentFaceUrlVo;
import com.senox.user.vo.ResidentSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/18 8:10
 */
@Service
@RequiredArgsConstructor
public class ResidentService extends ServiceImpl<ResidentMapper, Resident> {


    @Value("${senox.access.resident.prefix:AR}")
    private String residentNoPrefix;
    @Value("${senox.access.resident.length:5}")
    private Integer residentNoPostfixLength;
    @Value("${senox.access.serial.fill-char:0}")
    private Character fillChar;

    private final ResidentAccessService residentAccessService;

    /**
     * 添加住户
     *
     * @param resident
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String addResident(Resident resident) {
        Resident dbItem = findByIdNum(resident.getIdNum());
        if (dbItem != null) {
            throw new BusinessException("该住户已存在");
        }
        if (resident.getCreateTime() == null) {
            resident.setCreateTime(LocalDateTime.now());
        }
        if (resident.getModifiedTime() == null) {
            resident.setModifiedTime(LocalDateTime.now());
        }
        //初始化住户编号
        prepareResident(resident);
        save(resident);
        return resident.getResidentNo();
    }


    /**
     * 修改住户
     *
     * @param resident
     */
    public void updateResident(Resident resident) {
        if (!WrapperClassUtils.biggerThanLong(resident.getId(), 0L)) {
            return;
        }
        if (resident.getModifiedTime() == null) {
            resident.setModifiedTime(LocalDateTime.now());
        }
        resident.setFaceUrl(null);
        resident.setResidentNo(null);
        resident.setIdNum(null);
        updateById(resident);
    }

    /**
     * 更新住户头像
     *
     * @param residentFaceUrlVo
     * @param adminUserDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFaceUrl(ResidentFaceUrlVo residentFaceUrlVo, AdminUserDto adminUserDto) {
        Resident resident = new Resident();
        resident.setId(residentFaceUrlVo.getId());
        resident.setFaceUrl(residentFaceUrlVo.getFaceUrl());
        resident.setModifierId(adminUserDto.getUserId());
        resident.setModifierName(adminUserDto.getUsername());
        resident.setModifiedTime(LocalDateTime.now());
        updateById(resident);

        Resident dbItem = getById(residentFaceUrlVo.getId());
        List<ResidentAccess> accessList;
        if (dbItem != null) {
            accessList = residentAccessService.residentAccessByNo(dbItem.getResidentNo());
            residentAccessService.accessRight(dbItem.getResidentNo(), accessList, Boolean.FALSE);
        }
    }

    /**
     * 删除住户
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteResident(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        Resident dbItem = getById(id);
        List<ResidentAccess> accessList;
        if (dbItem != null) {
            accessList = residentAccessService.residentAccessByNo(dbItem.getResidentNo());
            deleteResidentAccess(dbItem.getResidentNo(), accessList);
        }
        removeById(id);
    }

    /**
     * 获取住户
     *
     * @param id
     * @return
     */
    public Resident findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据住户编号查询住户信息
     *
     * @param residentNo
     * @return
     */
    public Resident findByResidentNo(String residentNo) {
        return StringUtils.isBlank(residentNo)
                ? null
                : getOne(new QueryWrapper<Resident>().lambda().eq(Resident::getResidentNo, residentNo));
    }

    /**
     * 根据身份证查询住户
     *
     * @param idNum
     * @return
     */
    public Resident findByIdNum(String idNum) {
        return StringUtils.isBlank(idNum)
                ? null
                : getOne(new QueryWrapper<Resident>().lambda().eq(Resident::getIdNum, idNum));
    }

    /**
     * 住户列表
     *
     * @param search
     * @return
     */
    public PageResult<Resident> list(ResidentSearchVo search) {
        boolean isPage = Boolean.TRUE.equals(search.isPage());
        if (isPage && search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        // 总记录数
        int totalSize = 0;
        if (isPage) {
            totalSize = getBaseMapper().count(search);
            search.prepare();
            if (totalSize <= search.getOffset()) {
                return PageResult.emptyPage();
            }
        }
        // list
        List<Resident> resultList = getBaseMapper().list(search);
        if (totalSize == 0) {
            totalSize = resultList.size();
        }
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 检验用户
     *
     * @param residentCheckVo
     * @return
     */
    public Resident checkResident(ResidentCheckVo residentCheckVo) {
         return getOne(new QueryWrapper<Resident>().lambda().eq(Resident::getName, residentCheckVo.getName())
                .eq(Resident::getIdNum, residentCheckVo.getIdNum()).eq(Resident::getTelephone, residentCheckVo.getTelephone()));
    }

    /**
     * 删除权限
     *
     * @param residentNo
     * @param accessList
     */
    public void deleteResidentAccess(String residentNo, List<ResidentAccess> accessList) {
        if (!CollectionUtils.isEmpty(accessList)) {
            List<Long> ids = accessList.stream().map(ResidentAccess::getId).collect(Collectors.toList());
            residentAccessService.accessRight(residentNo, accessList, Boolean.TRUE);
            residentAccessService.removeByIds(ids);
        }
    }

    /**
     * 住户信息初始化
     *
     * @param resident
     */
    private void prepareResident(Resident resident) {
        // 订单号
        if (StringUtils.isBlank(resident.getResidentNo())) {
            String prefix = residentNoPrefix.concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
            resident.setResidentNo(prepareResidentNo(prefix, residentNoPostfixLength, fillChar));
        }
    }

    /**
     * 住户信息初始化
     *
     * @param prefix
     * @param length
     * @param fillChar
     * @return
     */
    private String prepareResidentNo(String prefix, int length, char fillChar) {
        Long dbSerial = findMaxResidentSerial(prefix);
        return prefix.concat(StringUtils.fixLength(String.valueOf(++dbSerial), length, fillChar));
    }

    /**
     * 获取最大住户序号
     *
     * @param prefix
     * @return
     */
    private Long findMaxResidentSerial(String prefix) {
        String maxResidentNo = getBaseMapper().findMaxResidentNo(prefix);
        return (StringUtils.isBlank(maxResidentNo) ? 0L : NumberUtils.parseLong(maxResidentNo.substring(prefix.length()), 0L));
    }
}
