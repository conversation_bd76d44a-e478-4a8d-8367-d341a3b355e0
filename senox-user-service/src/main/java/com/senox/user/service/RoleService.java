package com.senox.user.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.user.domain.Role;
import com.senox.user.domain.RoleCos;
import com.senox.user.mapper.AdminUserMapper;
import com.senox.user.mapper.CosItemMapper;
import com.senox.user.mapper.RoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/26 15:49
 */
@Service
public class RoleService {

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private CosItemMapper cosItemMapper;
    @Autowired
    private AdminUserMapper adminUserMapper;

    /**
     * 添加角色
     * @param role
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addRole(Role role, List<Long> cosList) {
        // 校验角色名唯一性
        if (checkRoleExists(null, role.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "角色已存在");
        }

        int result = roleMapper.addRole(role);
        // 保存角色权限
        if (result > 0 && !CollectionUtils.isEmpty(cosList)) {
            List<RoleCos> roleCosList = cosList.stream().map(x -> newRoleCos(role, x)).collect(Collectors.toList());
            roleMapper.batchAddRoleCos(roleCosList);
        }
        return result < 1 ? 0L : role.getId();
    }

    /**
     * 更新角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(Role role, List<Long> cosList) {
        if (!WrapperClassUtils.biggerThanLong(role.getId(), 0L)) {
            return false;
        }
        // 校验角色名唯一性
        if (checkRoleExists(role.getId(), role.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "角色已存在");
        }
        boolean result = roleMapper.updateRole(role) > 0;

        // 保存角色权限
        List<Long> originRoleCosLst = listRoleCos(role.getId());
        DataSepDto<Long> sepData = sepRoleCos(originRoleCosLst, cosList);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            List<RoleCos> addRoleCos = sepData.getAddList().stream()
                    .map(x -> this.newRoleCos(role, x))
                    .collect(Collectors.toList());
            roleMapper.batchAddRoleCos(addRoleCos);
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            roleMapper.batchDelRoleCos(role.getId(), sepData.getRemoveList());
        }

        return result;
    }

    /**
     * 删除角色
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return false;
        }
        if (checkRoleUsed(id)) {
            throw new BusinessException(ResultConst.ITEM_OCCUPIED, "角色已被引用，请先删除引用");
        }
        boolean result = roleMapper.deleteRole(id) > 0;
        if (result) {
            roleMapper.deleteRoleCos(id);
        }
        return result;
    }

    /**
     * 根据id查找角色
     * @param id
     * @return
     */
    public Role findRoleById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return roleMapper.findById(id);
    }

    /**
     * 角色列表
     * @return
     */
    public List<Role> listRoles() {
        return roleMapper.listAll();
    }

    /**
     * 角色权限列表
     */
    public List<Long> listRoleCos(Long roleId) {
        if (!WrapperClassUtils.biggerThanLong(roleId, 0L)) {
            return Collections.emptyList();
        }
        return roleMapper.listRoleCos(roleId);
    }

    /**
     * 角色是否存在
     * @param id
     * @param name
     * @return
     */
    private boolean checkRoleExists(Long id, String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        Long dbId = roleMapper.findIdByName(name);
        if (!WrapperClassUtils.biggerThanLong(dbId, 0L)) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, dbId);
    }


    /**
     * 比较角色权限，得出待修改记录
     * @param src
     * @param target
     * @return
     */
    private DataSepDto<Long> sepRoleCos(List<Long> src, List<Long> target) {
        DataSepDto<Long> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(target)) {
            // 目标记录为空，删掉所有原始记录
            result.setRemoveList(src);

        } else if (CollectionUtils.isEmpty(src)) {
            // 原始记录为空，新增所有目标记录
            result.setAddList(target);

        } else {
            // 新增所有在原始记录中没有的目标记录
            List<Long> addList = target.stream().filter(x -> !src.contains(x)).collect(Collectors.toList());
            // 移除所有在目标记录中没有的原始记录
            List<Long> removeList = src.stream().filter(x -> !target.contains(x)).collect(Collectors.toList());
            result.setAddList(addList);
            result.setRemoveList(removeList);
        }
        return result;
    }

    /**
     * 角色是否被占用
     * @param roleId
     * @return
     */
    private boolean checkRoleUsed(Long roleId) {
        return WrapperClassUtils.biggerThanLong(roleId, 0L) && adminUserMapper.countAdminUserRole(roleId) > 0;
    }

    /**
     * 新建角色权限关系
     * @param role
     * @param cosId
     * @return
     */
    private RoleCos newRoleCos(Role role, Long cosId) {
        RoleCos result = new RoleCos();
        result.setRoleId(role.getId());
        result.setCosId(cosId);
        result.setCreatorId(role.getModifierId());
        result.setCreatorName(role.getModifierName());
        result.setModifierId(role.getModifierId());
        result.setModifierName(role.getModifierName());
        return result;
    }
}
