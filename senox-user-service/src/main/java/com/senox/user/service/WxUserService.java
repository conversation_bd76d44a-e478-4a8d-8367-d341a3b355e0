package com.senox.user.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.vo.ContractVo;
import com.senox.user.constant.UserConst;
import com.senox.user.mapper.WxUserMapper;
import com.senox.user.vo.WxUserRealtyVo;
import com.senox.user.vo.WxUserRemarkVo;
import com.senox.user.vo.WxUserSearchVo;
import com.senox.user.vo.WxUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/7 14:07
 */
@Service
public class WxUserService {

    @Autowired
    private ContractComponent contractComponent;
    @Autowired
    private WxUserMapper wxUserMapper;

    /**
     * 更新微信用户信息
     * @param wxUser
     */
    public void updateWxUser(WxUserVo wxUser) {
        if (!WrapperClassUtils.biggerThanLong(wxUser.getId(), 0L)) {
            return;
        }
        boolean result = wxUserMapper.updateWxUser(wxUser) > 0;

        if (result) {
            // clear cache
            WxUserVo dbUser = wxUserMapper.findById(wxUser.getId());
            if (dbUser != null) {
                RedisUtils.del(String.format(UserConst.Cache.KEY_WXUSER, dbUser.getAppId(), dbUser.getOpenid()));
                RedisUtils.del(String.format(UserConst.Cache.KEY_WXUSER, StringUtils.EMPTY, dbUser.getOpenid()));
            }
        }
    }

    /**
     * 根据openid查找微信用户
     * @param appId
     * @param openid
     * @return
     */
    public WxUserVo findByOpenid(String appId, String openid) {
        return StringUtils.isBlank(openid) ? null : wxUserMapper.findByOpenid(appId, openid);
    }

    /**
     * 微信用户分页
     * @param searchVo
     * @return
     */
    public PageResult<WxUserVo> listWxUserPage(WxUserSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = wxUserMapper.countWxUser(searchVo);
        searchVo.prepare();

        if (totalSize <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<WxUserVo> resultList = wxUserMapper.listWxUser(searchVo);
        return PageUtils.resultPage(searchVo, totalSize, resultList);
    }

    /**
     * 微信用户绑定的物业
     * @param userId
     * @return
     */
    public List<WxUserRealtyVo> listWxUserRealty(Long userId) {
        return !WrapperClassUtils.biggerThanLong(userId, 0L) ? Collections.emptyList()
                : wxUserMapper.listWxUserRealty(userId);
    }

    /**
     * 绑定微信物业
     * @param userId
     * @param contractNo
     */
    public void bindWxUserRealty(Long userId, String contractNo) {
        ContractVo contract = contractComponent.findByContractNo(contractNo);
        if (contract == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到合同");
        }
        // 合同状态校验
        if (contract.getStatus() == ContractStatus.SUSPEND.ordinal()) {
            throw new BusinessException("合同已停用");
        }
        // 合同期限校验
        if (LocalDate.now().isAfter(contract.getEndDate().plusMonths(1L))) {
            throw new BusinessException("合同已过期");
        }

        WxUserRealtyVo userRealty = wxUserMapper.findWxUserRealty(userId, contract.getRealtyId());
        if (userRealty != null) {
            throw new BusinessException("物业已绑定");
        }

        WxUserRealtyVo result = new WxUserRealtyVo();
        result.setUserId(userId);
        result.setRealtyId(contract.getRealtyId());
        result.setRealtyName(contract.getRealtyName());
        result.setContractNo(contractNo);
        result.setStartDate(contract.getStartDate());
        result.setEndDate(contract.getEndDate());
        wxUserMapper.addWxUserRealty(result);
    }

    /**
     * 解绑微信用户物业关系
     * @param userId
     * @param realtyId
     */
    @Transactional(rollbackFor = Exception.class)
    public void unbindWxUserRealty(Long userId, Long realtyId) {
        WxUserRealtyVo userRealty = wxUserMapper.findWxUserRealty(userId, realtyId);
        if (userRealty == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到绑定记录");
        }

        // 解绑
        wxUserMapper.deleteWxUserRealty(userId, realtyId);
        // 添加解绑历史
        wxUserMapper.addUnbindHistory(userRealty);
    }

    /**
     * 更新微信账号备注
     * @param remarkVo
     */
    public void updateWxUserRemark(WxUserRemarkVo remarkVo) {
        wxUserMapper.updateWxUserRemark(remarkVo);
    }


    /**
     * 解绑骑手
     * @param riderId
     */
    public void unbindRider(Long riderId) {
        if (!WrapperClassUtils.biggerThanLong(riderId, 0L)) {
            return;
        }
        WxUserVo wxUserVo = wxUserMapper.findByRiderId(riderId);
        if (wxUserVo == null) {
            return;
        }
        wxUserMapper.unbindRider(wxUserVo.getOpenid());
        RedisUtils.del(String.format(UserConst.Cache.KEY_WXUSER, wxUserVo.getAppId(), wxUserVo.getOpenid()));
        RedisUtils.del(String.format(UserConst.Cache.KEY_WXUSER, StringUtils.EMPTY, wxUserVo.getOpenid()));
    }
}
