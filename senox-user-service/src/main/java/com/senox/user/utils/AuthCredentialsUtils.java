package com.senox.user.utils;

import java.util.Random;

/**
 * <AUTHOR>
 * @date 2024-3-4
 */
public class AuthCredentialsUtils {
    private static final String LETTERS_1 = "0123456789";
    private static final String LETTERS_2 = "ABCDEFabcdef0123456789";
    private static final Random random = new Random();
    private static final String APP_KEY_PREFIX = "sx";
    private static final int APP_KEY_LENGTH = 14;
    private static final int APP_SECRET_LENGTH = 32;

    private AuthCredentialsUtils() {
    }

    public static String generateAppKey() {
        StringBuilder builder = new StringBuilder();
        int length = APP_KEY_LENGTH;
        while (length > 0) {
            builder.append(LETTERS_1.charAt(random.nextInt(LETTERS_1.length())));
            length--;
        }
        return APP_KEY_PREFIX.concat(builder.toString());
    }

    public static String generateAppSecret() {
        StringBuilder builder = new StringBuilder();
        int length = APP_SECRET_LENGTH;
        while (length > 0) {
            builder.append(LETTERS_2.charAt(random.nextInt(LETTERS_2.length())));
            length--;
        }
        return builder.toString();
    }

}
