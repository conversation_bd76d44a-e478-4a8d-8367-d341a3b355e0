package com.senox.user.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Field;

/**
 * GlobalRedisAutoConfiguration  替换RedisUtils其中的Template
 */
public class GlobalRedisAutoConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(GlobalRedisAutoConfiguration.class);
    private static boolean AUTO_CONFIGURED = false;

    public static void autoConfigureRedisUtils() {
        if (AUTO_CONFIGURED) return;

        logger.info("开始Redis自动配置");

        try {
            RedisTemplate<String, Object> containerTemplate = getTemplate("com.senox.user.TestcontainersManager", "GLOBAL_REDIS_TEMPLATE");
            if (containerTemplate == null) {
                containerTemplate = getTemplate("com.senox.user.TestcontainersManagerSimple", "GLOBAL_REDIS_TEMPLATE");
            }
            if (containerTemplate == null) {
                containerTemplate = getTemplate("com.senox.user.TestcontainersManager", "GLOBAL_REDIS_TEMPLATE");
            }
            
            if (containerTemplate != null && replaceRedisUtils(containerTemplate)) {
                AUTO_CONFIGURED = true;
                logger.info("Redis自动配置成功");
                
                //只验证核心功能
                RedisTemplate<String, Object> redisUtilsTemplate = getRedisUtilsTemplate();
                if (redisUtilsTemplate == containerTemplate) {
                    logger.info("验证成功: hashCode={}", containerTemplate.hashCode());
                } else {
                    logger.warn("验证失败: 实例不匹配");
                }
            } else {
                logger.warn("Redis自动配置失败");
            }

        } catch (Exception e) {
            logger.error("Redis自动配置异常: {}", e.getMessage());
        }
    }

    /**
     * 统一的反射获取方法
     */
    private static RedisTemplate<String, Object> getTemplate(String className, String fieldName) {
        try {
            Class<?> clazz = Class.forName(className);
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return (RedisTemplate<String, Object>) field.get(null);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 替换RedisUtils - 只尝试最有效的方式
     */
    private static boolean replaceRedisUtils(RedisTemplate<String, Object> containerTemplate) {
        try {
            Class<?> redisUtilsClass = Class.forName("com.senox.common.utils.RedisUtils");
            Field instanceField = redisUtilsClass.getDeclaredField("redisUtils");
            instanceField.setAccessible(true);
            Object redisUtilsInstance = instanceField.get(null);
            
            if (redisUtilsInstance != null) {
                Field templateField = redisUtilsClass.getDeclaredField("redisTemplate");
                templateField.setAccessible(true);
                templateField.set(redisUtilsInstance, containerTemplate);
                return true;
            }
        } catch (Exception e) {
            logger.debug("替换失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 获取RedisUtils中的RedisTemplate
     */
    private static RedisTemplate<String, Object> getRedisUtilsTemplate() {
        try {
            Class<?> redisUtilsClass = Class.forName("com.senox.common.utils.RedisUtils");
            Field instanceField = redisUtilsClass.getDeclaredField("redisUtils");
            instanceField.setAccessible(true);
            Object redisUtilsInstance = instanceField.get(null);
            
            if (redisUtilsInstance != null) {
                Field templateField = redisUtilsClass.getDeclaredField("redisTemplate");
                templateField.setAccessible(true);
                return (RedisTemplate<String, Object>) templateField.get(redisUtilsInstance);
            }
        } catch (Exception e) {
            // 静默处理
        }
        return null;
    }

    public static boolean isAutoConfigured() {
        return AUTO_CONFIGURED;
    }
}
