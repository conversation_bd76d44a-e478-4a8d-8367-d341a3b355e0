package com.senox.user.utils;


import com.alibaba.nacos.common.utils.ThreadUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.user.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/1/8 15:59
 */
public class RedisUtilsTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtilsTest.class);

    @Test
    public void test() {
        RedisUtils.set("abc", "123");
        ThreadUtils.sleep(6000);
        String result = RedisUtils.get("abc");
        logger.info("result: {}", result);
        Assertions.assertEquals("123", result);
        RedisUtils.del("abc");
        Assertions.assertFalse(RedisUtils.hasKey("abc"));

        Assertions.assertEquals(2 * 60 * 60, TimeUnit.HOURS.toSeconds(2));
        Assertions.assertEquals(7 * 24 * 60 * 60, TimeUnit.DAYS.toSeconds(7));
    }

}